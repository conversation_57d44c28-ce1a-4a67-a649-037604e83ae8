package com.medusa.gruul.common.load.banlanced.template;

import cn.hutool.core.collection.CollUtil;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.lang.Nullable;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * date 2022/2/26
 */
public class TemplateAutoconfigure {

    @Bean
    @Primary
    public RestTemplate restTemplate(RestTemplateBuilder builder, @Nullable List<ClientHttpRequestInterceptor> interceptors){
        if (CollUtil.isEmpty(interceptors)){
            return builder.build();
        }
        return builder.interceptors(interceptors).build();
    }
}
