package com.medusa.gruul.common.dubbo.rpc.filter;

import com.medusa.gruul.global.model.exception.GlobalException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.rpc.*;
import org.apache.dubbo.rpc.service.GenericService;


/**
 * 自定义dubbo provider异常处理
 * <p>
 * 参考dubbo的异常过滤器
 *
 * <AUTHOR>
 * date 2022/7/15
 * @see org.apache.dubbo.rpc.filter.ExceptionFilter
 */
@Slf4j
@Activate(group = CommonConstants.PROVIDER)
public class DubboExceptionFilterBackup implements Filter, Filter.Listener {

    private static final String JAVA_PACKAGE_PREFIX = "java.";
    private static final String JAVAX_PACKAGE_PREFIX = "javax.";

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        return invoker.invoke(invocation);
    }

    @Override
    public void onResponse(Result appResponse, Invoker<?> invoker, Invocation invocation) {
        if (!appResponse.hasException() || GenericService.class == invoker.getInterface()) {
            return;
        }
        Throwable exception = appResponse.getException();
        //打印日志
        this.onError(exception, invoker, invocation);
        /* 非运行时异常的异常
         */
        if (!(exception instanceof RuntimeException) && (exception instanceof Exception)) {
            return;
        }
        /* 自定义异常
         */
        if (exception instanceof GlobalException) {
            return;
        }
        /* java jdk 内置异常
         */
        String className = exception.getClass().getName();
        if (className.startsWith(JAVA_PACKAGE_PREFIX) || className.startsWith(JAVAX_PACKAGE_PREFIX)) {
            return;
        }
        /* dubbo 内置异常
         */
        if (exception instanceof RpcException) {
            return;
        }
        /* 其他异常 toString 转为  runtime exception
         */
        appResponse.setException(new RuntimeException(StringUtils.toString(exception)));
    }

    @Override
    public void onError(Throwable e, Invoker<?> invoker, Invocation invocation) {
        log.error(
                "dubbo：异常------which called by " +
                        RpcContext.getServerContext().getRemoteHost() + ". service: " + invoker.getInterface().getName() +
                        ", method: " + invocation.getMethodName() +
                        ", exception: " + e.getClass().getName() + ": " + e.getMessage(),
                e
        );
    }
}
