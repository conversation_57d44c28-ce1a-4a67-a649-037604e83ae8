package com.medusa.gruul.common.mq.rabbit;

import lombok.Getter;
import lombok.Setter;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * date 2023/1/10
 */
@Getter
@Setter
@ConfigurationProperties(prefix = "gruul.rabbit")
public class CustomRabbitProperties {

    /**
     * 每次批量消费的数量 默认每批200个
     */
    private Integer batchSize = 300;

    /**
     * 批量消费ack确认模式 默认手动确认
     */
    private AcknowledgeMode batchAckMode = AcknowledgeMode.MANUAL;


}
