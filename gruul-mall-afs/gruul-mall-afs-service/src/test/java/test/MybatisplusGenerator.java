package test;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.medusa.gruul.common.mp.model.BaseEntity;

import java.util.HashMap;

/**
 * <AUTHOR>
 * date 2022/2/24
 */
public class MybatisplusGenerator {

    public static void main(String[] args) {
        FastAutoGenerator.create(
            "****************************************",
            "root",
            "public2020"
        )
            .globalConfig(builder -> {
                builder.author("张治保")
                    //.enableSwagger()
                    .outputDir("/Users/<USER>/Documents/project/zhongtai/gruul-mall-afs/gruul-mall-afs-service/src/main/java");
            })
            .packageConfig(builder -> {
                builder.parent("com.medusa.gruul.afs.api")
                    .moduleName("mp")
                    .pathInfo(
                        new HashMap<OutputFile, String>(){
                            {
                                put(OutputFile.xml,"/Users/<USER>/Documents/project/zhongtai/gruul-mall-afs/gruul-mall-afs-service/src/main/resources/mapper");
                            }
                        }
                    );
            })
            .strategyConfig(builder -> {
                builder.addInclude("t_afs_order","t_afs_package","t_afs_history","t_afs_order_item")
                    .addTablePrefix("t_")
                    .entityBuilder()
                    .enableLombok()
                .superClass(BaseEntity.class);
            }).execute();
    }
}
