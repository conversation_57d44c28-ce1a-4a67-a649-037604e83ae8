afs.express.company.not.null=\ 物流公司不能为空
afs.item.cannot.afs.status=该订单项处于不可申请售后状态
afs.item.closed=该订单项已关闭
afs.item.not.exist=该比订单不存在
afs.next.status.not.null=下一个售后状态不能为空
afs.not.default.return.address=未设置默认退货地址
afs.not.delivered.type.error=订单未发货，选择的售后类型不正确
afs.not.support.approval.status=无法审批当前售后状态
afs.not.support.close.status=不处于可关闭的售后状态
afs.not.support.deliver.type=不支持的发货方式
afs.not.support.refuse.return.status=不处于可拒绝退货的售后状态
afs.not.support.return.status=不处于可退货的售后状态
afs.refund.amount.cannot.greater.than.paid.amount=申请的退款金额不能大于订单实际支付金额
afs.status.not.match.order.status=售后工单状态和订单状态不匹配
afs.type.reason.not.match=售后类型和售后原因不匹配