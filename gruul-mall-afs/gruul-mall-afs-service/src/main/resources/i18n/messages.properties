afs.express.company.not.null=The express company cannot be empty
afs.item.cannot.afs.status=This order item is in a non applicable after-sales status
afs.item.closed=order item closed
afs.item.not.exist=This order does not exist
afs.next.status.not.null=The next after-sales status cannot be empty
afs.not.default.return.address=No default return address set
afs.not.delivered.type.error=The order has not been shipped, and the selected after-sales type is incorrect
afs.not.support.approval.status=Unable to approve current after-sales status
afs.not.support.close.status=Not in a switchable after-sales state
afs.not.support.deliver.type=Unsupported shipping method
afs.not.support.refuse.return.status=Not in an after-sales state where returns can be refused
afs.not.support.return.status=Not in a returnable after-sales state
afs.refund.amount.cannot.greater.than.paid.amount=The requested refund amount cannot be greater than the actual payment amount of the order
afs.status.not.match.order.status=The after-sales work order status does not match the order status
afs.type.reason.not.match=Mismatch between after-sales type and after-sales reason
