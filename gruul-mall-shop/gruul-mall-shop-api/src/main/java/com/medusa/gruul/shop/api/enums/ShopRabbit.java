package com.medusa.gruul.shop.api.enums;

import com.medusa.gruul.common.model.enums.RabbitParent;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * date 2022/5/25
 */
@RequiredArgsConstructor
public enum ShopRabbit implements RabbitParent {

    /**
     * 更换店铺管理员
     */
    SHOP_ADMIN_CHANGE("shop.admin.new"),
    /**
     * 启用店铺
     */
    SHOP_ENABLE_DISABLE("shop:enable_disable"),

    /**
     * 店铺信息 更新
     */
    SHOP_UPDATE("shop:update");

    private final String routingKey;

    public static final String EXCHANGE = "shop.direct";

    @Override
    public String exchange() {
        return EXCHANGE;
    }

    @Override
    public String routingKey() {
        return routingKey;
    }
}
