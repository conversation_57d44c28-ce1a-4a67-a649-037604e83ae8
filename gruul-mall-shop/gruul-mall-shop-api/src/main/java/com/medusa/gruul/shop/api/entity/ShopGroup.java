package com.medusa.gruul.shop.service.mp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.mp.model.BaseEntity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 店铺分组
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Getter
@Setter
@TableName("t_shop_group")
public class ShopGroup extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 备注
     */
    private String remark;
}
