package com.medusa.gruul.shop.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.custom.aggregation.decoration.entity.DecorateDetailsEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 店铺装修信息
 *
 * <AUTHOR>
 * @Description ShopDecorationDetails.java
 * @date 2022-08-17 13:55
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_shop_decoration_details")
public class ShopDecorationDetails extends DecorateDetailsEntity {

    /**
     * 装修主表id
     */
    private Long shopDecorationId;
    /**
     * 商户隔离id
     */
    private Long shopId;
}
