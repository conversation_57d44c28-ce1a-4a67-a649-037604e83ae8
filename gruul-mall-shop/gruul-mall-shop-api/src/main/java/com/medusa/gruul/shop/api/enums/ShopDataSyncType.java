package com.medusa.gruul.shop.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 同步变化类型
 *
 * <AUTHOR>
 */

@Getter
@RequiredArgsConstructor
public enum ShopDataSyncType {
    /**
     * 产品
     */
    PRODUCT(0),
    /**
     * 分销商品
     */
    DISTRIBUTOR_PRODUCT(1),
    /**
     * 装修
     */
    DECORATION(2),

    /**
     * 榜单推荐
     */
    RECOMMAND(3),

    /**
     * 热门商品
     */
    HOT(4),

    /**
     * 今日主推
     */
    TODAY(5),

    /**
     * 秒杀
     */
    SECKILL(5),

    ;

    @EnumValue
    private final Integer value;
}
