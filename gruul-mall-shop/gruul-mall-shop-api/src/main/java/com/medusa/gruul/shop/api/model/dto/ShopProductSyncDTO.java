package com.medusa.gruul.shop.api.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ShopProductSyncDTO implements Serializable {


    /**
     * 同步的目标店铺id, 为空查询所有
     */
    private Long targetShopId;

    /**
     * 需要同步的产品id, 为空查询所有
     */
    private Long productId;

}
