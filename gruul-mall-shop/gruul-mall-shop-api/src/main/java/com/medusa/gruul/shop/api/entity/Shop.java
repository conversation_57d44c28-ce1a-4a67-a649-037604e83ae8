package com.medusa.gruul.shop.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.geometry.GeometryTypeHandler;
import com.medusa.gruul.common.module.app.shop.ShopMode;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.global.model.enums.Mode;
import com.medusa.gruul.shop.api.enums.ExtractionType;
import com.medusa.gruul.shop.api.enums.ShopStatus;
import com.medusa.gruul.shop.api.enums.ShopType;
import com.vividsolutions.jts.geom.Point;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalTime;


/**
 * 商家注册信息
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "t_shop", autoResultMap = true)
public class Shop extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 店铺类型
	 */
	private ShopMode shopMode;

	/**
	 * 公司名称
	 */
	private String companyName;

	/**
	 * 店铺名称
	 */
	@TableField("`name`")
	private String name;

	/**
	 * 管理员用户id
	 */
	private Long userId;

	/**
	 * 店铺编号
	 */
	@TableField("`no`")
	private String no;

	/**
	 * 联系电话
	 */
	private String contractNumber;

	/**
	 * 状态 0.审核中,  1.正常, -1.禁用, -2审核拒绝
	 */
	@TableField("`status`")
	private ShopStatus status;

	/**
	 * 店铺类型
	 */
	private ShopType shopType;

	/**
	 * 抽取类型
	 */
	private ExtractionType extractionType;

	/**
	 * 抽成百分比
	 */
	private Integer drawPercentage;

	/**
	 * 联系地址
	 */
	private String address;

	/**
	 * 定位
	 */
	@TableField(typeHandler = GeometryTypeHandler.class)
	private Point location;

	/**
	 * logo url
	 */
	private String logo;
	/**
	 * 介绍
	 */
	private String briefing;
	/**
	 * 店铺头部背景
	 */
	private String headBackground;
	/**
	 * 营业开始时间
	 */
	@TableField("`start`")
	private LocalTime start;
	/**
	 * 营业结束时间
	 */
	@TableField("`end`")
	private LocalTime end;
	/**
	 * 上新提示
	 */
	private String newTips;

	/**
	 * 业务模式
	 */
	private Mode mode;


	/**
	 * 店铺银行账号信息
	 */
	@TableField(exist = false)
	private ShopBankAccount bankAccount;

}
