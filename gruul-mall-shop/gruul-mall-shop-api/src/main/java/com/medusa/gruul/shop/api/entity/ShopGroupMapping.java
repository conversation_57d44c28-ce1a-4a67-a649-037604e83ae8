package com.medusa.gruul.shop.service.mp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.mp.model.BaseEntity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 店铺与店铺分组关联关系
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Getter
@Setter
@TableName("t_shop_group_mapping")
public class ShopGroupMapping extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺分组id
     */
    private Long shopGroupId;
}
