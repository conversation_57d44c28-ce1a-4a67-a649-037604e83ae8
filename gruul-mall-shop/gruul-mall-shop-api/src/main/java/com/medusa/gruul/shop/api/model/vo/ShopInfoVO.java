package com.medusa.gruul.shop.api.model.vo;


import com.medusa.gruul.common.module.app.shop.ShopMode;
import com.medusa.gruul.global.model.enums.Mode;
import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.api.entity.ShopBankAccount;
import com.medusa.gruul.shop.api.enums.ExtractionType;
import com.medusa.gruul.shop.api.enums.ShopStatus;
import com.medusa.gruul.shop.api.enums.ShopType;
import com.vividsolutions.jts.geom.Point;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ShopInfoVO implements Serializable {

	/**
	 * 店铺id
	 */
	private Long id;

	/**
	 * 店铺运营模式
	 */
	private ShopMode shopMode;

	/**
	 * 业务模式
	 *
	 * @deprecated 请使用 {@link #shopMode}
	 */
	private Mode mode;

	/**
	 * 店铺类型
	 */
	private ShopType shopType;

	/**
	 * 店铺名称
	 */
	private String name;

	/**
	 * 店铺logo
	 */
	private String logo;

	/**
	 * 联系方式
	 */
	private String contractNumber;

	/**
	 * 上新提示
	 */
	private String newTips;

	/**
	 * 是否可用
	 */
	private ShopStatus status;

	/**
	 * 店铺头部背景
	 */
	private String headBackground;

	/**
	 * 距离
	 */
	private Double distance;

	/**
	 * '起送费'
	 */
	private BigDecimal initialDeliveryCharge;

	/**
	 * 销量
	 */
	private Long salesVolume;

	/**
	 * 定点经纬度
	 */
	private Point location;

	/**
	 * 联系地址
	 */
	private String address;

	/**
	 * 抽取类型
	 */
	private ExtractionType extractionType;

	/**
	 * 抽成百分比
	 */
	private Integer drawPercentage;
	/**
	 * 店铺银行账号信息
	 */
	private ShopBankAccount bankAccount;

	public static ShopInfoVO fromShop(Shop shop) {
		return new ShopInfoVO()
				.setId(shop.getId())
				.setShopMode(shop.getShopMode())
				.setMode(shop.getMode())
				.setShopType(shop.getShopType())
				.setName(shop.getName())
				.setContractNumber(shop.getContractNumber())
				.setLogo(shop.getLogo())
				.setNewTips(shop.getNewTips())
				.setStatus(shop.getStatus())
				.setDrawPercentage(shop.getDrawPercentage())
				.setExtractionType(shop.getExtractionType())
				.setLocation(shop.getLocation())
				.setHeadBackground(shop.getHeadBackground());
	}
}
