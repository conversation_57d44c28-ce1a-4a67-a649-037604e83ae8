package com.medusa.gruul.shop.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 同步变化类型
 *
 * <AUTHOR>
 */

@Getter
@RequiredArgsConstructor
public enum ShopDataStatus {
    /**
     * 同步中
     */
    SYNC_IN(0),
    /**
     * 成功
     */
    SUCCESS(1),
    /**
     * 失败
     */
    FAIL(2),


    ;

    @EnumValue
    private final Integer value;
}
