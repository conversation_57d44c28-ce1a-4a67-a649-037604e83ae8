package com.medusa.gruul.shop.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 同步变化类型
 *
 * <AUTHOR>
 */

@Getter
@RequiredArgsConstructor
public enum ShopDataSyncChangeType {
    /**
     * 新增
     */
    ADD(0),

    /**
     * 修改
     */
    UPDATE(1),

    /**
     * 删除
     */
    DELETE(2),

    ;

    @EnumValue
    private final Integer value;
}
