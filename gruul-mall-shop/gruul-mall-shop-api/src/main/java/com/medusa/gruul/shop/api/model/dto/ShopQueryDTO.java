package com.medusa.gruul.shop.api.model.dto;

import com.medusa.gruul.global.model.o.BaseDTO;
import com.medusa.gruul.shop.api.enums.ShopStatus;
import com.medusa.gruul.shop.api.enums.ShopType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ShopQueryDTO implements BaseDTO {
	/**
	 * 店铺id
	 */
	private Long id;

	/**
	 * 店铺编号
	 */
	private String no;

	/**
	 * 店铺状态
	 */
	private ShopStatus status;
	/**
	 * 店铺类型
	 */
	private ShopType shopType;

}
