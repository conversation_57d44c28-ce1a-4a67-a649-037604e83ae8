package com.medusa.gruul.shop.api.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.mp.FastJson2TypeHandler;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.shop.api.enums.ShopDataStatus;
import com.medusa.gruul.shop.api.enums.ShopDataSyncChangeType;
import com.medusa.gruul.shop.api.enums.ShopDataSyncType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 商品同步记录表
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "t_shop_data_sync_record", autoResultMap = true, excludeProperty = "deleted")
public class ShopDataSyncRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 同步的数据id
     */
    private Long syncDataId;

    /**
     * 同步的数据no
     */
    private String syncDataNo;
    /**
     * 同步原始数据
     */
    @TableField(typeHandler = FastJson2TypeHandler.class)
    private JSONObject syncData;

    /**
     * 同步的产品对应店铺
     */
    private Long shopId;

    /**
     * 目标店铺id
     */
    private Long targetShopId;

    /**
     * 目标数据id
     */
    private Long targetDataId;

    /**
     * 目标数据编号
     */
    private String targetDataNo;

    /**
     * 同步类型
     */
    private ShopDataSyncType syncType;

    /**
     * 变化类型
     */
    private ShopDataSyncChangeType syncChangeType;

    /**
     * 变化数据
     */
    @TableField(typeHandler = FastJson2TypeHandler.class)
    private JSONObject syncChangeData;

    /**
     * 同步装态
     */
    private ShopDataStatus status;
    /**
     * 同步信息，如异常
     */
    @TableField(typeHandler = FastJson2TypeHandler.class)
    private JSONObject syncMessage;

}
