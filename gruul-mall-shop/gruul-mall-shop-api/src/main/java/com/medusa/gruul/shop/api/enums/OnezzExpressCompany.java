package com.medusa.gruul.shop.api.enums;

import com.medusa.gruul.global.model.exception.GlobalException;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 快递100 物流编码
 */
@RequiredArgsConstructor
@Getter
public enum OnezzExpressCompany {
    YUANTONG("yuantong", "圆通速递", "国内运输商"),
    ZHONGTONG("zhongtong", "中通快递", "国内运输商"),
    JT("jtexpress", "极兔速递", "国内运输商"),
    YUNDA("yunda", "韵达快递", "国内运输商"),
    SHENTONG("shentong", "申通快递", "国内运输商"),
    YOUZHENG("youzhengguonei", "邮政快递包裹", "国际邮政"),
    SHUNFENG("shunfeng", "顺丰速运", "国际运输商"),
    JD("jd", "京东物流", "国内运输商"),
    EMS("ems", "EMS", "国际邮政"),
    DEBANG("debangkuaidi", "德邦快递", "国内运输商"),
    YOUZHENGDSBK("youzhengdsbk", "邮政电商标快", "国内运输商"),
    YOUZHENGBK("youzhengbk", "邮政标准快递", "国内运输商"),
    DANNIAO("danniao", "菜鸟速递", "国内运输商"),
    DEBANGWL("debangwuliu", "德邦物流", "国内运输商"),
    ZHONGTONGKY("zhongtongkuaiyun", "中通快运", "国内运输商"),
    KUAYUE("kuayue", "跨越速运", "国内运输商"),
    ANNENG("annengwuliu", "安能快运", "国内运输商"),
    SHUNFENGKY("shunfengkuaiyun", "顺丰快运", "国内运输商"),
    JDKY("jingdongkuaiyun", "京东快运", "国内运输商");

    private final String expressCode;
    private final String expressName;
    private final String category;

    /**
     * 根据物流名称返回code
     *
     * @param expressName
     * @return
     */
    public static OnezzExpressCompany ofNameMust(String expressName) {
        for (OnezzExpressCompany w : OnezzExpressCompany.values()) {
            if (w.getExpressName().equals(expressName)) {
                return w;
            }
        }
        throw new GlobalException("未匹配到微信对应的物流");
    }

    public static OnezzExpressCompany ofName(String expressName) {
        for (OnezzExpressCompany w : OnezzExpressCompany.values()) {
            if (w.getExpressName().equals(expressName)) {
                return w;
            }
        }
        return null;
    }

}
