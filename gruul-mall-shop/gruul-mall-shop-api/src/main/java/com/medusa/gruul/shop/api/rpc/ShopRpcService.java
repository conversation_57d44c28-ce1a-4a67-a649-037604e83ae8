package com.medusa.gruul.shop.api.rpc;

import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.api.entity.ShopDataSyncRecord;
import com.medusa.gruul.shop.api.enums.ShopDataSyncType;
import com.medusa.gruul.shop.api.model.dto.ShopQueryDTO;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.api.model.vo.ShopLogisticsAddressVO;
import com.medusa.gruul.shop.service.mp.entity.ShopGroup;
import io.vavr.control.Option;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface ShopRpcService {

	/**
	 * 根据店铺id查询店铺基本信息
	 *
	 * @param shopId 店铺id
	 * @return 店铺基本信息
	 */
	ShopInfoVO getShopInfoByShopId(@NotNull Long shopId);


	/**
	 * 根据店铺id集合批量获取店铺基本信息
	 *
	 * @param shopIds 店铺id集合
	 * @return 店铺基本信息
	 */
	List<ShopInfoVO> getShopInfoByShopIdList(@NotNull @Size(min = 1) Set<Long> shopIds);


	/**
	 * 根据店铺id集合批量获取店铺基本信息与银行信息
	 *
	 * @param shopIds 店铺id集合
	 * @return 店铺基本信息
	 */
	List<ShopInfoVO> getShopAndShopBankInfoByShopIdList(@NotNull @Size(min = 1) Set<Long> shopIds);


	/**
	 * 根据店铺id查询店铺与店铺银行账号信息
	 *
	 * @param shopId 店铺id
	 * @return 店铺与其银行账号信息
	 */
	Option<Shop> getShopAndShopBankInfo(@NotNull Long shopId);

	/**
	 * 获取 默认的发货地址/退货地址
	 *
	 * @param shopId 店铺id
	 * @param isSend 是否是收货地址
	 * @return 发货地址/收货地址
	 */
	ShopLogisticsAddressVO getSendOrReceiveAddress(@NotNull Long shopId, @NotNull Boolean isSend);

	/**
	 * 获取所有正常店铺
	 * @return
	 */
	List<Shop> getShop(ShopQueryDTO shopQueryDTO);

	/**
	 * 批量创建同步记录
	 * @param shopDataSyncRecordList
	 */
	void createShopSyncRecord(List<ShopDataSyncRecord> shopDataSyncRecordList);
	/**
	 * 查询目标店铺最新的同步记录(相同的sync_data_id只取最新一条)
	 * @param targetShopId 目标店铺id
	 * @return
	 */
	List<ShopDataSyncRecord> getLatestRecordByTargetShopId(Long targetShopId, ShopDataSyncType syncType);

	/**
	 * 获取店铺所属组
	 * @return
	 */
	ShopGroup getShopGroupByShopId(Long shopId);
}
