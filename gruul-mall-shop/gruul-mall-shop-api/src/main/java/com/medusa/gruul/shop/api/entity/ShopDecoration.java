package com.medusa.gruul.shop.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.custom.aggregation.decoration.entity.DecorateEntity;
import com.medusa.gruul.common.custom.aggregation.decoration.enums.AggregationPlatform;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 
 *  店铺装修表
 * 
 *
 * <AUTHOR>
 * @Description  ShopDecoration.java
 * @date 2022-08-17 13:42
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_shop_decoration")
public class ShopDecoration  extends DecorateEntity {


}
