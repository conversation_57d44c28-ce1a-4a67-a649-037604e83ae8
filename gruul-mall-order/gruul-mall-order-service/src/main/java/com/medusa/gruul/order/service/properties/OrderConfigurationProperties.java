package com.medusa.gruul.order.service.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * date 2022/6/10
 */
@Getter
@Setter
@ConfigurationProperties(prefix = "gruul.order")
public class OrderConfigurationProperties {

    /**
     * 缓存时间配置
     */
    private CacheExpire cacheExpire = new CacheExpire();
    /**
     * 线程池配置
     */
    private TaskThreadPool threadPool = new TaskThreadPool();

    /**
     * 缓存 过期时间 单位秒
     */
    @Getter
    @Setter
    public static class CacheExpire {
        /**
         * 创建订单缓存时间 单位秒
         */
        private long createOrderCache = 20 * 60;
        /**
         * sku 库存 限购 缓存时间 默认两天 单位秒
         */
        private long stockLimit = 2 * 24 * 60 * 60;
    }

    /**
     * 线程池配置详情
     */
    @Getter
    @Setter
    public static class TaskThreadPool {

        /**
         * 线程池线程名前缀
         */
        private String threadNamePrefix = "Order-Future";
        /**
         * 核心线程数 - 适合4核CPU的I/O密集型任务
         */
        private int corePoolSize = 8;
        /**
         * 最大线程数 - 处理突发流量
         */
        private int maxPoolSize = 16;
        /**
         * 线程存活时间长度 - 空闲线程保持时间(秒)
         */
        private int keepAliveSeconds = 60;
        /**
         * 任务队列长度
         */
        private int queueCapacity = 1000;
    }

}
