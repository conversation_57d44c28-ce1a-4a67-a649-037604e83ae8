package com.medusa.gruul.order.service.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.medusa.gruul.order.service.properties.OrderConfigurationProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * date 2022/6/10
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class OrderConfig {

    private final OrderConfigurationProperties orderConfigProperties;

    @Bean
    public TaskExecutor orderTaskExecutor() {
        OrderConfigurationProperties.TaskThreadPool taskThreadPool = orderConfigProperties.getThreadPool();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix(taskThreadPool.getThreadNamePrefix());
        executor.setCorePoolSize(taskThreadPool.getCorePoolSize());
        executor.setMaxPoolSize(taskThreadPool.getMaxPoolSize());
        executor.setQueueCapacity(taskThreadPool.getQueueCapacity());
        executor.setKeepAliveSeconds(taskThreadPool.getKeepAliveSeconds());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();

        // 记录线程池配置信息
        log.info("Order线程池配置 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}, 空闲存活时间: {}秒",
                taskThreadPool.getCorePoolSize(),
                taskThreadPool.getMaxPoolSize(),
                taskThreadPool.getQueueCapacity(),
                taskThreadPool.getKeepAliveSeconds());

        return executor;
    }

    /**
     * 异步执行线程池
     */
    @Bean
    public Executor orderExecutor() {
        return TtlExecutors.getTtlExecutor(orderTaskExecutor());
    }

}
