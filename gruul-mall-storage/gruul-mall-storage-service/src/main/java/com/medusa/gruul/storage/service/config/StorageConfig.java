package com.medusa.gruul.storage.service.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.medusa.gruul.storage.service.properties.StorageProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * date 2022/7/7
 */
@Slf4j
@RequiredArgsConstructor
@Configuration
public class StorageConfig {

    
    private final StorageProperties storageProperties;

    @Bean
    public TaskExecutor storageTaskExecutor() {
        StorageProperties.TaskThreadPool taskThreadPool = storageProperties.getTaskThreadPool();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix(taskThreadPool.getThreadNamePrefix());
        executor.setCorePoolSize(taskThreadPool.getCorePoolSize());
        executor.setMaxPoolSize(taskThreadPool.getMaxPoolSize());
        executor.setQueueCapacity(taskThreadPool.getQueueCapacity());
        executor.setKeepAliveSeconds(taskThreadPool.getKeepAliveSeconds());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();

        // 记录线程池配置信息
        log.info("Storage线程池配置 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}, 空闲存活时间: {}秒",
                taskThreadPool.getCorePoolSize(),
                taskThreadPool.getMaxPoolSize(),
                taskThreadPool.getQueueCapacity(),
                taskThreadPool.getKeepAliveSeconds());

        return executor;
    }

    /**
     * 异步执行线程池 completableTaskExecutor
     */
    @Bean
    public Executor storageExecutor() {
        return TtlExecutors.getTtlExecutor(storageTaskExecutor());
    }
}
