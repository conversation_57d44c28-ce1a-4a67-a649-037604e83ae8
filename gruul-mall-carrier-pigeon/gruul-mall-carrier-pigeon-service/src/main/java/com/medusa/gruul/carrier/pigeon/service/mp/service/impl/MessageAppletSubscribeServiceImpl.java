package com.medusa.gruul.carrier.pigeon.service.mp.service.impl;

import com.medusa.gruul.carrier.pigeon.service.mp.entity.MessageAppletSubscribe;
import com.medusa.gruul.carrier.pigeon.service.mp.mapper.MessageAppletSubscribeMapper;
import com.medusa.gruul.carrier.pigeon.service.mp.service.IMessageAppletSubscribeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-09
 */
@Service
public class MessageAppletSubscribeServiceImpl extends ServiceImpl<MessageAppletSubscribeMapper, MessageAppletSubscribe> implements IMessageAppletSubscribeService {

}
