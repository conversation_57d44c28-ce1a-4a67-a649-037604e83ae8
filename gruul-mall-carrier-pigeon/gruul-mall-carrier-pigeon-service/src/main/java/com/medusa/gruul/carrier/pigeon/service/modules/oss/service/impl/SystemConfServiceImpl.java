package com.medusa.gruul.carrier.pigeon.service.modules.oss.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.carrier.pigeon.api.oss.constant.OssConstant;
import com.medusa.gruul.carrier.pigeon.api.oss.entity.SystemConf;
import com.medusa.gruul.carrier.pigeon.api.oss.enums.CloudServiceEnum;
import com.medusa.gruul.carrier.pigeon.api.oss.enums.StorageType;
import com.medusa.gruul.carrier.pigeon.api.oss.model.dto.OssConfigDto;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.config.AliyunStorageConfig;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.config.QiniouStorageConfig;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.config.QuidwayStorageConfig;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.config.TencentStorageConfig;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.mapper.SystemConfMapper;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.model.vo.SystemConfigVo;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.service.ISystemConfService;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.system.model.ISystem;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description:
 * @Author: xiaoq
 * @Date : 2022-03-24 09:18
 */
@Service
public class SystemConfServiceImpl extends ServiceImpl<SystemConfMapper, SystemConf> implements ISystemConfService {

    /**
     * oss配置编辑  新增or修改
     *
     * @param editOssConfigDto oss配置信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editOssConf(OssConfigDto editOssConfigDto) {
        StorageType type = editOssConfigDto.getType();
        SystemConf systemConf = new SystemConf();
        String paramKey = "";
        switch (type.getValue()) {
            case 1:
                paramKey = OssConstant.STORAGE_QINIOUYUN;
                systemConf.setParamValue(JSON.toJSONString(editOssConfigDto));
                break;
            case 2:
                paramKey = OssConstant.STORAGE_ALIYUN;
                systemConf.setParamValue(JSON.toJSONString(editOssConfigDto));
                break;
            case 3:
                paramKey = OssConstant.STORAGE_TENCENT_CLOUD;
                systemConf.setParamValue(JSON.toJSONString(editOssConfigDto));
                break;
            case 4:
                paramKey = OssConstant.STORAGE_QUIDWAY_CLOUD;
                systemConf.setParamValue(JSON.toJSONString(editOssConfigDto));
                break;
            default:
                throw new ServiceException("数据修改异常", SystemCode.DATA_UPDATE_FAILED.getCode());
        }
        SystemConf ossConf = this.getByParamKey(OssConstant.CURRENT_OSS_TYPE);
        if (this.baseMapper.update(systemConf, new QueryWrapper<SystemConf>().eq(OssConstant.PARAM_KEY, paramKey)) == 0) {
            systemConf.setParamKey(paramKey);
            this.baseMapper.insert(systemConf);
        }
        if (ossConf == null) {
            ossConf = new SystemConf();
            ossConf.setParamKey(OssConstant.CURRENT_OSS_TYPE);
        }
        ossConf.setParamValue(type.toString());
//        //根据type获取当前使用dto 存入redis
        Result<OssConfigDto> ossConfigDtoResult = ossConfig(type.getValue());

        //获取来源设置oss存储信息 设置进redis
        Long shopId = ISystem.shopId().must();
        RedisUtil.setCacheObject(OssConstant.OSS_KEY, ossConfigDtoResult.getData());
        this.saveOrUpdate(ossConf);
    }

    /**
     * 获取当前使用oss配置
     */
    @Override
    public Result<OssConfigDto> currentOssConfig() {
        SystemConf ossConf = this.getByParamKey(OssConstant.CURRENT_OSS_TYPE);
        Integer type = CommonPool.NUMBER_ONE;
        if (ossConf != null) {
            type = Integer.valueOf(ossConf.getParamValue());
        }
        return ossConfig(type);
    }

    @Override
    public Result<OssConfigDto> ossConfig(Integer type) {
        SystemConfigVo typeInfo = getTypeInfo(type);
        if (BeanUtil.isEmpty(typeInfo)) {
            return Result.failed("当前oss配置为空无法使用");
        }
        OssConfigDto dto = new OssConfigDto();

        if (type.equals(CloudServiceEnum.QINIU.getType()) && typeInfo.getQiniouStorageConfig() != null) {
            BeanUtils.copyProperties(typeInfo.getQiniouStorageConfig(), dto);
            dto.setType(StorageType.QINIUO);
        } else if (type.equals(CloudServiceEnum.ALIYUN.getType()) && typeInfo.getAliyunStorageConfig() != null) {
            BeanUtils.copyProperties(typeInfo.getAliyunStorageConfig(), dto);
            dto.setType(StorageType.ALIYUN);
        } else if (type.equals(CloudServiceEnum.QCLOUD.getType()) && typeInfo.getTencentStorageConfig() != null) {
            BeanUtils.copyProperties(typeInfo.getTencentStorageConfig(), dto);
            dto.setType(StorageType.TENCENT);
        } else if (type.equals(CloudServiceEnum.QUIDWAY.getType()) && typeInfo.getQuidwayStorageConfig() != null) {
            BeanUtil.copyProperties(typeInfo.getQuidwayStorageConfig(), dto);
            dto.setType(StorageType.QUIDWAY);
        }
        return Result.ok(dto);
    }

    private SystemConfigVo getTypeInfo(Integer type) {

        String paramKey = "";
        if (type.equals(CloudServiceEnum.QINIU.getType())) {
            paramKey = OssConstant.STORAGE_QINIOUYUN;
        } else if (type.equals(CloudServiceEnum.ALIYUN.getType())) {
            paramKey = OssConstant.STORAGE_ALIYUN;
        } else if (type.equals(CloudServiceEnum.QCLOUD.getType())) {
            paramKey = OssConstant.STORAGE_TENCENT_CLOUD;
        } else if (type.equals(CloudServiceEnum.QUIDWAY.getType())) {
            paramKey = OssConstant.STORAGE_QUIDWAY_CLOUD;
        } else {
            throw new ServiceException("数据获取异常", SystemCode.DATA_NOT_EXIST.getCode());
        }
        SystemConfigVo systemConfigVo = new SystemConfigVo();
        SystemConf systemConf = this.getByParamKey(paramKey);
        if (systemConf == null) {
            return systemConfigVo;
        }
        if (type.equals(CloudServiceEnum.QINIU.getType())) {
            QiniouStorageConfig qiniouStorageConfig = JSON.parseObject(systemConf.getParamValue(), QiniouStorageConfig.class);
            systemConfigVo.setQiniouStorageConfig(qiniouStorageConfig);
        } else if (type.equals(CloudServiceEnum.ALIYUN.getType())) {
            AliyunStorageConfig aliyunStorageConfig = JSON.parseObject(systemConf.getParamValue(), AliyunStorageConfig.class);
            systemConfigVo.setAliyunStorageConfig(aliyunStorageConfig);
        } else if (type.equals(CloudServiceEnum.QCLOUD.getType())) {
            TencentStorageConfig tencentStorageConfig = JSON.parseObject(systemConf.getParamValue(), TencentStorageConfig.class);
            systemConfigVo.setTencentStorageConfig(tencentStorageConfig);
        } else if (type.equals(CloudServiceEnum.QUIDWAY.getType())) {
            QuidwayStorageConfig quidwayStorageConfig = JSON.parseObject(systemConf.getParamValue(), QuidwayStorageConfig.class);
            systemConfigVo.setQuidwayStorageConfig(quidwayStorageConfig);
        }
        return systemConfigVo;
    }


    private SystemConf getByParamKey(String paramKey) {
        return this.lambdaQuery()
                .eq(SystemConf::getParamKey, paramKey)
                .one();
    }
}
