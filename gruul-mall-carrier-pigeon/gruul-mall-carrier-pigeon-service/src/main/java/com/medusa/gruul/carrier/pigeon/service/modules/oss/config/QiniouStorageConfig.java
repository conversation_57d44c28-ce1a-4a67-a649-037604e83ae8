package com.medusa.gruul.carrier.pigeon.service.modules.oss.config;

import lombok.Data;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class QiniouStorageConfig {

    /**
     * 七牛绑定的域名
     */
    @NotBlank(message = "七牛绑定的域名不能为空")
    @URL(message = "七牛绑定的域名格式不正确")
    private String qiniuDomain;
    /**
     * 七牛路径前缀
     */
    private String qiniuPrefix;
    /**
     * 七牛ACCESS_KEY
     */
    @NotBlank(message = "七牛AccessKey不能为空")
    private String qiniuAccessKey;
    /**
     * 七牛SecretKey不能为空
     */
    @NotBlank(message = "七牛SecretKey不能为空")
    private String qiniuSecretKey;
    /**
     * 七牛存储空间名
     */
    @NotBlank(message = "七牛空间名不能为空")
    private String qiniuBucketName;
}
