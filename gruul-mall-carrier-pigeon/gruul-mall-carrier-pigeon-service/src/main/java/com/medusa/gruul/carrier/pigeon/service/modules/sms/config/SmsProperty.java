package com.medusa.gruul.carrier.pigeon.service.modules.sms.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/12/8
 */
@Component
@Data
@RefreshScope
public class SmsProperty {
    @Value("${sms.conf}")
    private String currentSmsTypeKey;
}
