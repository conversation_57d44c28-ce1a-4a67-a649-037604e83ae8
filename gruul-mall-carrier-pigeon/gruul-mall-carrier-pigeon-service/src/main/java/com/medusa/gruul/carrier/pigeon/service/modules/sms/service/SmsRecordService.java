package com.medusa.gruul.carrier.pigeon.service.modules.sms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.carrier.pigeon.api.sms.dto.SmsRecordDto;
import com.medusa.gruul.carrier.pigeon.api.sms.entity.SmsRecord;
import com.medusa.gruul.carrier.pigeon.api.sms.vo.SmsRecordVo;

/**
 * <AUTHOR>
 * @date 2022/12/12
 */
public interface SmsRecordService extends IService<SmsRecord> {
    /**
     * 查询短信记录
     *
     * @param smsRecordDto
     * @return
     */
    IPage<SmsRecordVo> getSmsRecord(SmsRecordDto smsRecordDto);
}
