package com.medusa.gruul.carrier.pigeon.service.modules.oss.controller;

import com.medusa.gruul.carrier.pigeon.api.oss.entity.FileEntity;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.cloud.AbstractCloudStorageService;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.cloud.OSSFactory;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.service.IFileService;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

/**
 * 文件上传控制器
 *
 * @Author: xiaoq
 * @Date : 2022-03-05 09:51
 */
@RestController
@RequestMapping("/oss")
@Slf4j
public class FileUploadController {

    @Autowired
    private IFileService fileService;

    @Autowired
    private OSSFactory ossFactory;

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    @PreAuthorize("@S.authenticated")
    public Result upload(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new ServiceException("上传文件不能为空");
        }
        //上传文件
        String suffix = FilenameUtils.getExtension(file.getOriginalFilename());

        AbstractCloudStorageService build = ossFactory.build();
        //
        long l = System.currentTimeMillis();
        String url = build
                .uploadSuffix(file.getBytes(), suffix);
        log.warn("上传使用时间".concat(String.valueOf(System.currentTimeMillis() - l)));
        //保存文件信息
        FileEntity fileEntity = new FileEntity();
        fileEntity.setUrl(url);
        fileEntity.setOriginalName(file.getOriginalFilename());
        fileEntity.setSize(file.getSize());
        fileEntity.setSuffix(suffix);
        fileEntity.setCreateDate(new Date());
        fileService.save(fileEntity);
        Result<String> ok = Result.ok(url);
        ok.setMsg(file.getOriginalFilename());
        return ok;
    }


}
