package com.medusa.gruul.carrier.pigeon.service.im.repository;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.medusa.gruul.carrier.pigeon.service.im.constants.IMRedisConstant;
import com.medusa.gruul.carrier.pigeon.service.im.entity.UserInfo;
import com.medusa.gruul.common.redis.util.RedisUtil;
import org.springframework.stereotype.Repository;

/**
 * 基于Redis实现的用户信息持久层
 * <AUTHOR>
 */
@Repository
public class UserInfoRepository {

    /**
     * 保存用户信息到Redis
     * @param userInfo 用户信息,参考 {@link UserInfo}
     */
    public void saveUser(UserInfo userInfo) {
        RedisUtil.getRedisTemplate().opsForHash().put(
                IMRedisConstant.IM_USERINFO_KEY,
                userInfo.getUserKey(), JSON.toJSONString(userInfo));
    }

    /**
     * 根据ID获取用户信息
     * @param userId 用户ID
     * @return {@link UserInfo}
     */
    public UserInfo getUserById(Long userId) {
        Object userObj = RedisUtil.getRedisTemplate().opsForHash().get(IMRedisConstant.IM_USERINFO_KEY, String.valueOf(userId));
        if (userObj == null) {
            return null;
        }
        return JSONObject.parseObject(String.valueOf(userObj), UserInfo.class);
    }
}
