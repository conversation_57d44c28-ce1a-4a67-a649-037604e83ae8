package com.medusa.gruul.carrier.pigeon.service.service.impl;

import com.medusa.gruul.carrier.pigeon.service.service.EmailSendService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class EmailSenderImpl implements EmailSendService {

    @Value("${spring.mail.username:<EMAIL>}")
    private String account;

    private final JavaMailSender javaMailSender;

    @Override
    public void sendMessage(String text, String header, String email) throws MessagingException {
        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true);
        helper.setFrom(account);
        helper.setTo(email);
        helper.setSubject(header);
        helper.setText(text);
        javaMailSender.send(message);
    }
}
