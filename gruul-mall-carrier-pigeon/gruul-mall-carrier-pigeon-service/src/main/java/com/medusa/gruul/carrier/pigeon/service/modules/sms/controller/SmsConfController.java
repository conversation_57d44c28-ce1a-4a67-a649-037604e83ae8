package com.medusa.gruul.carrier.pigeon.service.modules.sms.controller;

import com.medusa.gruul.carrier.pigeon.api.sms.dto.SmsConfigDto;
import com.medusa.gruul.carrier.pigeon.api.sms.dto.SmsCurrentConfigDto;
import com.medusa.gruul.carrier.pigeon.api.sms.entity.SmsSign;
import com.medusa.gruul.carrier.pigeon.api.sms.entity.SmsTemplate;
import com.medusa.gruul.carrier.pigeon.api.sms.enums.StorageType;
import com.medusa.gruul.carrier.pigeon.service.modules.sms.service.SmsSignService;
import com.medusa.gruul.common.model.resp.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;


/**
 * sms配置
 *
 * <AUTHOR>
 * Date : 2022-03-29 18:16
 */
@RestController
@RequestMapping("/sms")
@RequiredArgsConstructor
@PreAuthorize("@S.platformPerm('generalSet')")
public class SmsConfController {
    private final SmsSignService smsSignService;


    /**
     * 新增或修改短信签名
     *
     * @param smsSign 短信签名
     */
    @PostMapping("/saveAndEdit/smsSign")
    public Result<Void> saveAndEditSmsSign(@RequestBody @Validated SmsSign smsSign) {
        smsSignService.saveAndEditSmsSign(smsSign);
        return Result.ok();
    }

    /**
     * 新增或修改短信模板
     *
     * @param smsTemplate 短信模板
     */
    @PostMapping("/saveAndEdit/smsTemplate")
    public Result<Void> saveAndEditSmsTemplate(@RequestBody @Validated SmsTemplate smsTemplate) {
        smsSignService.saveAndEditSmsTemplate(smsTemplate);
        return Result.ok();
    }

    /**
     * 根据type 获取短信配置信息
     *
     * @param type 查询类型
     */
    @GetMapping("current/config")
    public Result<SmsConfigDto> smsConfig(@RequestParam(name = "type") StorageType type) {
        SmsConfigDto smsConfigDtoList = smsSignService.smsConfig(type);
        return Result.ok(smsConfigDtoList);
    }

    /**
     * @param smsCurrentConfigDto 短信配置
     * @return
     */
    @PostMapping("/saveAndEdit/current/config")
    public Result<Void> saveAndEditSmsConfig(@RequestBody @NotNull(message = "短信配置不能为空") SmsCurrentConfigDto smsCurrentConfigDto) {
        smsSignService.saveAndEditSmsConfig(smsCurrentConfigDto);
        return Result.ok();
    }

}
