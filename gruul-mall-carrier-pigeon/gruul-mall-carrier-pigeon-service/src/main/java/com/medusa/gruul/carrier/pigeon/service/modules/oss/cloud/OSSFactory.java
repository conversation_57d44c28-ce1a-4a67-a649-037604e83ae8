package com.medusa.gruul.carrier.pigeon.service.modules.oss.cloud;


import com.medusa.gruul.carrier.pigeon.api.oss.constant.OssConstant;
import com.medusa.gruul.carrier.pigeon.api.oss.enums.StorageType;
import com.medusa.gruul.carrier.pigeon.api.oss.model.dto.OssConfigDto;
import com.medusa.gruul.common.fastjson2.FastJson2;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.global.model.exception.GlobalException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 文件上传Factory
 *
 * @<NAME_EMAIL>
 */
@Component
@Slf4j
public class OSSFactory {


    public AbstractCloudStorageService build() {
        //获取云存储配置信息
        Map ossConfigDto = RedisUtil.getCacheObject(OssConstant.OSS_KEY);
        OssConfigDto dto = FastJson2.convert(ossConfigDto, OssConfigDto.class);
        if (dto == null) {
            throw new GlobalException("未读取到 OSS配置");
        }
        log.debug("存储信息".concat(dto.toString()));
        if (dto.getType().equals(StorageType.QINIUO)) {
            return new QiniuCloudStorageService(dto);
        } else if (dto.getType().equals(StorageType.ALIYUN)) {
            return new AliyunCloudStorageService(dto);
        } else if (dto.getType().equals(StorageType.TENCENT)) {
            return new QcloudCloudStorageService(dto);
        } else if (dto.getType().equals(StorageType.QUIDWAY)) {
            return new QuidwayCloundStorageService(dto);
        }
        return null;
    }

}
