package com.medusa.gruul.carrier.pigeon.service.modules.oss.config;

import lombok.Data;

import java.io.Serializable;

/**
 * 华为云配置
 *
 * @Author: xiaoq
 * @Date : 2022-04-11 17:06
 */
@Data
public class QuidwayStorageConfig implements Serializable {
    private static final long serialVersionUID = -4521898139569541597L;

    /**
     * 华为云的 Access Key Id
     */
    private String quidwayAccessKeyId;


    /**
     * 华为云的 Access Key Secret
     */
    private String quidwayAccessKeySecret;

    /**
     * 华为云连接的地址节点
     */
    private String quidwayEndpoint;

    /**
     * 华为云存储桶名称
     */
    private String obsBucketName;

    /**
     * 华为云绑定的域名
     */
    private String quidwayDomain;

    /**
     * 华为云路径前缀
     */
    private String quidwayPrefix;
}
