package com.medusa.gruul.carrier.pigeon.service.im.repository;


import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.carrier.pigeon.service.im.entity.ChatMessage;
import com.medusa.gruul.carrier.pigeon.service.im.entity.GroupChatRoom;
import com.medusa.gruul.carrier.pigeon.service.im.model.dto.SingleChatListQueryDTO;
import com.medusa.gruul.common.redis.util.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.Set;
import java.util.stream.Collectors;


/**
 * 基于Redis实现的消息持久层
 * <AUTHOR>
 */
@Repository
@Slf4j
@RequiredArgsConstructor
public class MessageRepository {

    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 根据分页信息,获取指定的聊天室的消息记录
     * @param groupChatRoom 聊天室对象,参见{@link GroupChatRoom}
     * @param query 分页对象
     * @return {@link ChatMessage}
     */
    public IPage<ChatMessage> listMessageByRoomId(GroupChatRoom groupChatRoom, SingleChatListQueryDTO query) {
        log.info("获取群聊天室聊天记录, param = {}", JSON.toJSONString(query));
        Long total = stringRedisTemplate.opsForZSet().size(groupChatRoom.getGroupChatRoomId());
        if (total <= 0) {
            log.warn("聊天室 = {}, 不存在或暂无聊天记录");
            return new Page<>(0, 0);
        }
        long start = (query.getCurrent() - 1) * query.getSize();
        long end = query.getCurrent() * query.getSize();
        IPage<ChatMessage> result = new Page<>(query.getCurrent(), query.getSize(), total);
        Set<String> msgObjects = stringRedisTemplate.opsForZSet().reverseRange(groupChatRoom.getGroupChatRoomId(), start, end);
        result.setRecords(msgObjects
                .stream()
                .map(e -> JSON.parseObject(String.valueOf(e), ChatMessage.class))
                .collect(Collectors.toList()));
        return result;
    }


    /**
     * 获取指定聊天室的消息
     * @param groupChatRoom {@link GroupChatRoom}
     * @param start 消息起始索引
     * @param end 消息结束索引
     * @return {@link Set}
     */
    public Set<String> listChatMessageByRoomId(GroupChatRoom groupChatRoom, int start, int end) {
        return stringRedisTemplate
                .opsForZSet()
                .reverseRange(groupChatRoom.getGroupChatRoomId(), start, end);
    }

    /**
     * 发送消息到聊天室
     * @param destination 聊天室对象
     * @param chatMessage 消息对象
     */
    public void sendMessage(GroupChatRoom destination, ChatMessage chatMessage) {
        stringRedisTemplate
                .opsForZSet()
                .add(
                        destination.getGroupChatRoomId(),
                        JSON.toJSONString(chatMessage),
                        chatMessage.getSendTime()
                );
    }
}
