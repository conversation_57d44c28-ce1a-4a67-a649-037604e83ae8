package com.medusa.gruul.carrier.pigeon.service.mp.service.impl;

import com.medusa.gruul.carrier.pigeon.service.mp.entity.PigeonShopMessage;
import com.medusa.gruul.carrier.pigeon.service.mp.mapper.PigeonShopMessageMapper;
import com.medusa.gruul.carrier.pigeon.service.mp.service.IPigeonShopMessageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 公告与店铺关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-26
 */
@Service
public class PigeonShopMessageServiceImpl extends ServiceImpl<PigeonShopMessageMapper, PigeonShopMessage> implements IPigeonShopMessageService {

}
