package com.medusa.gruul.carrier.pigeon.service.im.model.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.medusa.gruul.carrier.pigeon.api.enums.ChatWithType;
import com.medusa.gruul.carrier.pigeon.service.model.vo.MessageShopVO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;


/**
 * <p>供应商-店铺消息列表DTO</p>
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class PlatformShopAndUserPageQueryDTO extends Page<MessageShopVO> {

    private Long shopId;

    private String keywords;

    private Long userId;

    @NotNull
    private Integer chatWithType;
}
