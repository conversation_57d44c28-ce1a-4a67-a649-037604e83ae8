package com.medusa.gruul.carrier.pigeon.service.modules.oss.cloud;


import com.aliyun.oss.OSSClient;
import com.medusa.gruul.carrier.pigeon.api.oss.model.dto.OssConfigDto;
import com.medusa.gruul.common.model.exception.ServiceException;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * 阿里云存储
 *
 * <AUTHOR> sunlight<PERSON>@gmail.com
 */
public class AliyunCloudStorageService extends AbstractCloudStorageService {
    private OSSClient client;

    public AliyunCloudStorageService(OssConfigDto config) {
        this.config = config;

        //初始化
        init();
    }

    private void init() {
        client = new OSSClient(config.getAliyunEndPoint(), config.getAliyunAccessKeyId(),
                config.getAliyunAccessKeySecret());
    }

    @Override
    public String upload(byte[] data, String path) {
        return upload(new ByteArrayInputStream(data), path);
    }

    @Override
    public String upload(InputStream inputStream, String path) {
        try {
            client.putObject(config.getAliyunBucketName(), path, inputStream);
        } catch (Exception e) {
            throw new ServiceException("上传文件失败，请检查配置信息", e);
        }

        return config.getAliyunDomain() + "/" + path;
    }

    @Override
    public String uploadSuffix(byte[] data, String suffix) {
        return upload(data, getPath(config.getAliyunPrefix(), suffix));
    }

    @Override
    public String uploadSuffix(InputStream inputStream, String suffix) {
        return upload(inputStream, getPath(config.getAliyunPrefix(), suffix));
    }
}
