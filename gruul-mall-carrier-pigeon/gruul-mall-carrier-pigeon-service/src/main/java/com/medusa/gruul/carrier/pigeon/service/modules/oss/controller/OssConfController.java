package com.medusa.gruul.carrier.pigeon.service.modules.oss.controller;

import com.medusa.gruul.carrier.pigeon.api.oss.enums.StorageType;
import com.medusa.gruul.carrier.pigeon.api.oss.model.dto.OssConfigDto;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.service.ISystemConfService;
import com.medusa.gruul.common.model.resp.Result;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * oss配置控制器
 *
 * @Author: xiaoq
 * @Date : 2022-03-07 13:41
 */
@RestController
@RequestMapping("/oss")
@PreAuthorize("@S.platformPerm('generalSet')")
public class OssConfController {
    @Resource
    private ISystemConfService systemConfService;

    /**
     * 保存或更新oss配置
     *
     * @param editOssConfigDto oss配置信息
     */
    @PostMapping("edit")
    public Result<Void> editOssConf(@RequestBody @Valid OssConfigDto editOssConfigDto) {
        systemConfService.editOssConf(editOssConfigDto);
        return Result.ok();
    }

    /**
     * 获取当前使用oss配置
     *
     * @return OssConfigDto
     */
    @GetMapping("config")
    @PreAuthorize("permitAll()")
    public Result<OssConfigDto> getOssConfInfo() {
        return systemConfService.currentOssConfig();
    }


    /**
     * 根据类型获取oss配置
     *
     * @param type 查询类型 0-当前使用的配置 1：七牛  2：阿里云  3：腾讯云
     * @return OssConfigDto
     */
    @GetMapping("current/config")
    public Result<OssConfigDto> ossConfig(@RequestParam(name = "type") StorageType type) {
        return systemConfService.ossConfig(type.getValue());

    }


}
