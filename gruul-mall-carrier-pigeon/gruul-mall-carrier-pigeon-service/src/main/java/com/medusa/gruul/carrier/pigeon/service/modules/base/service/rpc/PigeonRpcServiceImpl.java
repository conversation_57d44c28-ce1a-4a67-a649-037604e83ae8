package com.medusa.gruul.carrier.pigeon.service.modules.base.service.rpc;

import com.medusa.gruul.carrier.pigeon.api.rpc.PigeonRpcService;
import com.medusa.gruul.carrier.pigeon.service.service.EmailSendService;
import kotlin.jvm.Throws;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@DubboService
@RequiredArgsConstructor
public class PigeonRpcServiceImpl implements PigeonRpcService {

    private final EmailSendService emailSendService;

    @SneakyThrows
    @Override
    public void sendMessage(String text, String header, String email) {
        emailSendService.sendMessage(text, header, email);
    }

}
