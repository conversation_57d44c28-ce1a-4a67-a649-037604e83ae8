package com.medusa.gruul.carrier.pigeon.service.im.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.carrier.pigeon.api.enums.SendType;
import com.medusa.gruul.carrier.pigeon.api.enums.UserType;
import com.medusa.gruul.carrier.pigeon.service.im.entity.*;
import com.medusa.gruul.carrier.pigeon.service.im.model.dto.PlatformShopAndUserMessageDTO;
import com.medusa.gruul.carrier.pigeon.service.im.model.dto.PlatformShopAndUserPageQueryDTO;
import com.medusa.gruul.carrier.pigeon.service.im.model.dto.PlatformShopAndUserSingeRoomQueryDTO;
import com.medusa.gruul.carrier.pigeon.service.im.model.vo.PlatformShopAndUserMultiChatRoomVO;
import com.medusa.gruul.carrier.pigeon.service.im.repository.PlatformShopAndUserChatRoomRepository;
import com.medusa.gruul.carrier.pigeon.service.im.repository.PlatformShopAndUserMessageRepository;
import com.medusa.gruul.carrier.pigeon.service.im.service.CarrierUserService;
import com.medusa.gruul.carrier.pigeon.service.im.service.IPlatformShopAndUserChatMessageService;
import com.medusa.gruul.carrier.pigeon.service.im.service.IShopService;
import com.medusa.gruul.carrier.pigeon.service.service.MessageSender;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.system.model.model.ClientType;
import com.medusa.gruul.global.model.helper.CompletableTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.regex.Pattern;

import static com.medusa.gruul.carrier.pigeon.api.enums.ChatWithType.PLAT_FORM;
import static com.medusa.gruul.carrier.pigeon.api.enums.ChatWithType.SHOP;

/**
 * <p>平台/店铺-用户聊天接口实现类,参考{@link IPlatformShopAndUserChatMessageService}</p>
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PlatformShopAndUserChatMessageImpl implements IPlatformShopAndUserChatMessageService {

    private final PlatformShopAndUserChatRoomRepository chatRoomRepository;
    private final PlatformShopAndUserMessageRepository messageRepository;
    private final IShopService shopService;
    private final CarrierUserService userService;
    private final Executor pigeonCompletableTaskExecutor;
    private final MessageSender messageSender;

    /**
     * 平台/店铺-用户创建聊天室
     *
     * @param shopId 店铺Id
     * @param userId 用户ID
     */
    @Override
    public void createRoom(Long shopId, Long userId) {
        // 检查店铺&User
        if (CommonPool.NUMBER_ZERO.longValue() != shopId) {
            shopService.checkAndGetMessageShop(shopId);
        }
        userService.checkAndGetUserInfo(userId);
        // 创建聊天室
        Long currTime = System.currentTimeMillis();
        chatRoomRepository.createRoom(new PlatformShopAndUserChatRoom(shopId, userId, currTime));
        chatRoomRepository.createRoom(new PlatformShopAndUserChatRoom(userId, shopId, currTime));
    }

    /**
     * 分页查询平台/店铺与C端用户聊天室列表
     *
     * @param dto {@link PlatformShopAndUserPageQueryDTO}
     * @return {@link PlatformShopAndUserMultiChatRoomVO}
     */
    @Override
    public IPage<PlatformShopAndUserMultiChatRoomVO> listMultiChatRoom(PlatformShopAndUserPageQueryDTO dto) {
        IPage<PlatformShopAndUserMultiChatRoomVO> result = new Page<>();

        // 根据client type 创建ChatRoom对象
        ClientType clientType = ISystem.clientTypeMust();
        PlatformShopAndUserChatRoom chatRoom;
        switch (clientType) {
            case SHOP_CONSOLE, PLATFORM_CONSOLE ->
                    chatRoom = new PlatformShopAndUserChatRoom(ISecurity.userMust().getShopId());
            case CONSUMER -> chatRoom = new PlatformShopAndUserChatRoom(dto.getUserId());
            default -> throw new ServiceException("无效的Client-Type");
        }
        
        // 如果非平台端则检查店铺信息
        if (dto.getShopId() != null && CommonPool.NUMBER_ZERO.longValue() != dto.getShopId()) {
            shopService.checkAndGetMessageShop(dto.getShopId());
        }

        // 根据分页信息获取店铺聊天室
        Set<String> chatRoomSet = chatRoomRepository.listMultiChatRoom(chatRoom, dto);
        List<PlatformShopAndUserMultiChatRoomVO> chatRoomHolder = new ArrayList<>();

        // 遍历聊天室Set,设置每个聊天室最后一条消息
        Pattern roomIdPattern = Pattern.compile("^\\d+:\\d+$");
        Long chatId = dto.getShopId() == null ? dto.getUserId() : dto.getShopId();
        chatRoomSet.stream().filter(e -> roomIdPattern.matcher(e).matches()).forEach(item -> {
            Long fromId = Long.parseLong(item.split(":")[0]);
            Long toId = Long.parseLong(item.split(":")[1]);
            Long chatWithId = chatId.longValue() == fromId.longValue() ? toId : fromId;

            // 如果是C端用户在和店铺聊天,排除掉平台消息
            if (dto.getChatWithType().equals(SHOP.getValue()) && chatWithId == CommonPool.NUMBER_ZERO.longValue()) {
                return;
            }

            // 获取聊天室最后一条消息
            PlatformShopAndUserChatRoom currChatRoom = new PlatformShopAndUserChatRoom(fromId, toId, null);
            Set<String> messageSet = messageRepository.listChatMessageByRoomId(currChatRoom, 0, 1);

            // 根据chat with type设置聊天属性
            PlatformShopAndUserMultiChatRoomVO chatRoomVO;
            if (dto.getChatWithType().equals(SHOP.getValue()) || dto.getChatWithType().equals(PLAT_FORM.getValue())) {
                chatRoomVO = new PlatformShopAndUserMultiChatRoomVO()
                        .setChatWithShopInfo(shopService.checkAndGetMessageShop(chatWithId));
                // keywords模糊匹配群聊名称
                if (!StringUtils.isEmpty(dto.getKeywords()) &&
                        !chatRoomVO.getChatWithShopInfo().getShopName().contains(dto.getKeywords())) {
                    return;
                }
            } else {
                chatRoomVO = new PlatformShopAndUserMultiChatRoomVO()
                        .setChatWithUserInfo(userService.checkAndGetUserInfo(chatWithId));
                // keywords模糊匹配群聊名称
                if (!StringUtils.isEmpty(dto.getKeywords()) &&
                        !chatRoomVO.getChatWithUserInfo().getNickname().contains(dto.getKeywords())) {
                    return;
                }
            }

            // 设置lastMessage
//            if (!CollectionUtils.isEmpty(messageSet)) {
                chatRoomHolder
                        .add(chatRoomVO.setLastMessage(
                                        JSON.parseObject(messageSet.stream().findFirst().orElse(null), ChatMessage.class)
                                )
                        );
//            }
        });
        result.setRecords(chatRoomHolder);
        return result;
    }

    /**
     * 获取平台/店铺-用户聊天室聊天记录
     *
     * @param dto {@link PlatformShopAndUserSingeRoomQueryDTO}
     * @return {@link ChatMessage}
     */
    @Override
    public IPage<ChatMessage> listSingleChatRoomMessages(PlatformShopAndUserSingeRoomQueryDTO dto) {
        // 根据ClientType判断客户端类型
        PlatformShopAndUserChatRoom chatRoom = buildChatRoom(dto.getShopId(), dto.getUserId());
        return this.messageRepository.listMessageByRoomId(chatRoom, dto);
    }

    private PlatformShopAndUserChatRoom buildChatRoom(Long shopId, Long userId) {
        ClientType clientType = ISystem.clientTypeMust();
        PlatformShopAndUserChatRoom chatRoom;
        switch (clientType) {
            case SHOP_CONSOLE, CONSUMER -> {
                chatRoom = new PlatformShopAndUserChatRoom(shopId, userId, null);
            }
            case PLATFORM_CONSOLE -> {
                chatRoom = new PlatformShopAndUserChatRoom(0L, userId, null);
            }
            default -> {
                throw new ServiceException("无效的Client-Type");
            }
        }
        return chatRoom;
    }

    /**
     * 平台/店铺-用户发送消息
     *
     * @param dto {@link PlatformShopAndUserMessageDTO}
     */
    @Override
    public void sendMessage(PlatformShopAndUserMessageDTO dto) {
        Long currTime = System.currentTimeMillis();

        // 根据client type组装对象
        ClientType clientType = ISystem.clientTypeMust();
        Long shopId, userId;
        PlatformShopAndUserChatRoom senderChatRoom, receiverChatRoom;
        ChatMessageSender chatMessageSender = new ChatMessageSender();
        ChatMessageReceiver chatMessageReceiver = new ChatMessageReceiver();
        String destinationTopic, destinationTopic2 = null;
        switch (clientType) {
            case SHOP_CONSOLE -> {
                shopId = dto.getSenderId();
                userId = dto.getReceiverId();
                senderChatRoom = new PlatformShopAndUserChatRoom(shopId, userId, currTime);
                receiverChatRoom = senderChatRoom.reverse();
                chatMessageSender.setSenderType(UserType.SHOP_ADMIN).setSenderShopInfo(getShopInfo(shopId));
                chatMessageReceiver.setReceiverType(UserType.CONSUMER).setReceiverUserInfo(getUserInfo(userId));
                destinationTopic = SendType.H5_USER.getDestination(CommonPool.NUMBER_ZERO.longValue(), userId);
                destinationTopic2 = SendType.MARKED_SHOP.getDestination(shopId);
            }
            case PLATFORM_CONSOLE -> {
                shopId = dto.getSenderId();
                userId = dto.getReceiverId();
                senderChatRoom = new PlatformShopAndUserChatRoom(0L, userId, currTime);
                receiverChatRoom = senderChatRoom.reverse();
                chatMessageSender.setSenderType(UserType.PLATFORM_ADMIN).setSenderShopInfo(getShopInfo(shopId));
                chatMessageReceiver.setReceiverType(UserType.CONSUMER).setReceiverUserInfo(getUserInfo(userId));
                destinationTopic = SendType.H5_USER.getDestination(CommonPool.NUMBER_ZERO.longValue(), userId);
                destinationTopic2 = SendType.MARKED_PLATFORM.getDestination(shopId);
            }
            case CONSUMER -> {
                shopId = dto.getReceiverId();
                userId = dto.getSenderId();
                senderChatRoom = new PlatformShopAndUserChatRoom(shopId, userId, currTime);
                receiverChatRoom = senderChatRoom.reverse();
                chatMessageSender.setSenderType(UserType.CONSUMER).setSenderUserInfo(getUserInfo(userId));
                chatMessageReceiver.setReceiverType(UserType.SHOP_ADMIN).setReceiverShopInfo(getShopInfo(shopId));
                if (shopId != CommonPool.NUMBER_ZERO.longValue()) {
                    destinationTopic = SendType.MARKED_SHOP.getDestination(shopId);
                } else {
                    destinationTopic = SendType.MARKED_PLATFORM.getDestination(shopId);
                }
            }
            default -> throw new ServiceException("无效的Client-Type");
        }

        // 检查聊天室
        if (!this.chatRoomRepository.checkChatRoomExist(senderChatRoom) ||
                !this.chatRoomRepository.checkChatRoomExist(receiverChatRoom)) {
            return;
        }

        ChatMessage chatMessage = new ChatMessage()
                .setMessage(StrUtil.trim(dto.getContent()))
                .setMessageType(dto.getMessageType())
                .setRead(Boolean.FALSE)
                .setShow(Boolean.TRUE)
                .setHandled(Boolean.FALSE)
                .setSendTime(currTime)
                .setSender(chatMessageSender)
                .setReceiver(chatMessageReceiver);

        // 发送消息
        this.messageRepository.sendMessage(senderChatRoom, chatMessage);

        // 更新聊天室
        this.chatRoomRepository.updateRoom(senderChatRoom);
        this.chatRoomRepository.updateRoom(senderChatRoom);

        // 异步发送到WS
        String finalDestinationTopic = destinationTopic;
        String finalDestinationTopic2 = destinationTopic2;
        CompletableTask.allOf(
                pigeonCompletableTaskExecutor,
                () -> messageSender.send(
                        finalDestinationTopic,
                        new PlatformShopAndUserMessage().setSender(chatMessage.getSender())
                                .setSenderTime(currTime)
                                .setRead(Boolean.FALSE)
                                .setReceiver(chatMessage.getReceiver())
                                .setMessage(chatMessage.getMessage())
                                .setMessageType(chatMessage.getMessageType())
                ),
                () -> messageSender.send(
                        finalDestinationTopic2,
                        new PlatformShopAndUserMessage().setSender(chatMessage.getSender())
                                .setSenderTime(currTime)
                                .setRead(Boolean.FALSE)
                                .setReceiver(chatMessage.getReceiver())
                                .setMessage(chatMessage.getMessage())
                                .setMessageType(chatMessage.getMessageType())
                )
        );
    }

    @Nullable
    private ShopInfo getShopInfo(Long shopId) {
        return Optional.ofNullable(shopId)
                .map(e -> CommonPool.NUMBER_ZERO.longValue() == e.longValue() ?
                        new ShopInfo(String.valueOf(CommonPool.NUMBER_ZERO.longValue()), "平台", null) :
                        shopService.checkAndGetMessageShop(e))
                .orElse(null);
    }

    @Nullable
    private UserInfo getUserInfo(Long userId) {
        return Optional.ofNullable(userId)
                .map(e -> userService.checkAndGetUserInfo(e))
                .orElse(null);
    }
}
