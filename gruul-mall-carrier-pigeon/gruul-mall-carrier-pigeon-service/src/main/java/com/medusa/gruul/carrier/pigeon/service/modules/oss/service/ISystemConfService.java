package com.medusa.gruul.carrier.pigeon.service.modules.oss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.carrier.pigeon.api.oss.entity.SystemConf;
import com.medusa.gruul.carrier.pigeon.api.oss.model.dto.OssConfigDto;
import com.medusa.gruul.common.model.resp.Result;

/**
 * <AUTHOR>
 * 系统配置
 */
public interface ISystemConfService extends IService<SystemConf> {
    /**
     * 保存或更新oss配置
     *
     * @param editOssConfigDto oss配置信息
     */
    void editOssConf(OssConfigDto editOssConfigDto);

    /**
     * 获取当前使用oss配置
     *
     * @return OssConfigDto
     */
    Result<OssConfigDto> currentOssConfig();

    /**
     * 获取指定类型的存储配置
     *
     * @param type 查询类型 0-当前使用的配置 1：七牛  2：阿里云  3：腾讯云
     * @return OssConfigDto
     */
    Result<OssConfigDto> ossConfig(Integer type);
}
