package com.medusa.gruul.carrier.pigeon.service.modules.oss.model;

import com.medusa.gruul.carrier.pigeon.api.oss.enums.StorageType;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.config.AliyunStorageConfig;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.config.QiniouStorageConfig;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.config.QuidwayStorageConfig;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.config.TencentStorageConfig;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;

/**
 * 云存储配置信息
 *
 * @Author: xiaoq
 * @Date : 2022-03-07 14:45
 */
@Data
public class EditOssConfigDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 保存类型  1：七牛  2：阿里云  3：腾讯云 4：华为云
     */
    private StorageType type;
    /**
     * 阿里云配置
     */
    @Valid
    private AliyunStorageConfig aliyunStorageConfig;
    /**
     * 七牛云配置
     */
    @Valid
    private QiniouStorageConfig qiniouStorageConfig;
    /**
     * 腾讯云配置
     */
    @Valid
    private TencentStorageConfig tencentStorageConfig;
    /**
     * 华为云配置
     */
    @Valid
    private QuidwayStorageConfig quidwayStorageConfig;
}
