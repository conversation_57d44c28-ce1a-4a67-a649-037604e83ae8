package com.medusa.gruul.carrier.pigeon.service.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.medusa.gruul.carrier.pigeon.service.properties.PigeonConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * date 2022/10/18
 */
@Configuration
public class PigeonConfig {

    /**
     * 异步执行线程池
     */
    @Bean
    public Executor pigeonCompletableTaskExecutor(PigeonConfigurationProperties configurationProperties) {
        PigeonConfigurationProperties.TaskThreadPool taskThreadPool = configurationProperties.getThreadPool();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix(taskThreadPool.getThreadNamePrefix());
        executor.setCorePoolSize(taskThreadPool.getCorePoolSize());
        executor.setMaxPoolSize(taskThreadPool.getMaxPoolSize());
        executor.setQueueCapacity(taskThreadPool.getQueueCapacity());
        executor.setKeepAliveSeconds(taskThreadPool.getKeepAliveSeconds());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return TtlExecutors.getTtlExecutor(executor);
    }

}
