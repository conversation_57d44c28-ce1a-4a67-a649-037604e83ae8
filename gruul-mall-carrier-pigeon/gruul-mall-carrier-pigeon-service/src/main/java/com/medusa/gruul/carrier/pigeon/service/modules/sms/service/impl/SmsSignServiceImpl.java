package com.medusa.gruul.carrier.pigeon.service.modules.sms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.qcloudsms.SmsSingleSender;
import com.github.qcloudsms.SmsSingleSenderResult;
import com.medusa.gruul.carrier.pigeon.api.sms.constant.SmsConstant;
import com.medusa.gruul.carrier.pigeon.api.sms.dto.EditSmsConfigDto;
import com.medusa.gruul.carrier.pigeon.api.sms.dto.SmsConfigDto;
import com.medusa.gruul.carrier.pigeon.api.sms.dto.SmsCurrentConfigDto;
import com.medusa.gruul.carrier.pigeon.api.sms.dto.SmsSendDto;
import com.medusa.gruul.carrier.pigeon.api.sms.entity.SmsRecord;
import com.medusa.gruul.carrier.pigeon.api.sms.entity.SmsSign;
import com.medusa.gruul.carrier.pigeon.api.sms.entity.SmsTemplate;
import com.medusa.gruul.carrier.pigeon.api.sms.enums.SmsTemplateType;
import com.medusa.gruul.carrier.pigeon.api.sms.enums.StorageType;
import com.medusa.gruul.carrier.pigeon.service.modules.sms.config.SmsProperty;
import com.medusa.gruul.carrier.pigeon.service.modules.sms.mapper.SmsSignMapper;
import com.medusa.gruul.carrier.pigeon.service.modules.sms.service.SmsRecordService;
import com.medusa.gruul.carrier.pigeon.service.modules.sms.service.SmsSignService;
import com.medusa.gruul.carrier.pigeon.service.modules.sms.service.SmsTemplateService;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.SystemCode;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * date 2022/12/9
 */
@Service
@RequiredArgsConstructor
public class SmsSignServiceImpl extends ServiceImpl<SmsSignMapper, SmsSign> implements SmsSignService {

    private final SmsProperty smsProperty;

    private final SmsTemplateService smsTemplateService;

    private final SmsRecordService smsRecordService;

    /**
     * 发送短信
     *
     * @param smsSendDto 参数
     */
    @Override
    public void smsSend(SmsSendDto smsSendDto) {
        String providerName = SmsConstant.ALI_CODE.equals(smsProperty.getCurrentSmsTypeKey()) ? SmsConstant.ALI : SmsConstant.TENCENT;
        List<EditSmsConfigDto> smsConfigs = this.baseMapper.getSmsConfig(smsSendDto.getTemplateType(), providerName);
        if (CollUtil.isEmpty(smsConfigs)) {
            throw new ServiceException("当前短信配置不存在!", SystemCode.DATA_NOT_EXIST_CODE);
        }
        EditSmsConfigDto smsConfig = smsConfigs.get(0);
        ArrayList<String> params = new ArrayList<>();
        params.add(smsSendDto.getSmsSendParam());
        //腾讯
        if (SmsConstant.TENCENT.equals(smsConfig.getProviderName())) {
            SmsSingleSender sender = new SmsSingleSender(Integer.parseInt(smsConfig.getProviderAppId()), smsConfig.getProviderAppSecret());
            try {
                SmsSingleSenderResult smsSingleSenderResult =
                        sender.sendWithParam(CommonPool.NATION_CODE, smsSendDto.getSmsSendMobiles(), Integer.parseInt(smsConfig.getTemplateCode()), params, smsConfig.getSignature(), "", "");
                log.warn("smsSingleSenderResult: " + smsSingleSenderResult);
                if (!"OK".equals(smsSingleSenderResult.errMsg)) {
                    SmsRecord smsRecord = new SmsRecord();
                    smsRecord.setSmsSendMobiles(smsSendDto.getSmsSendMobiles())
                            .setSmsSendParam(smsSendDto.getSmsSendParam())
                            .setSignId(smsConfig.getSmsSignId())
                            .setIsErr(Boolean.TRUE)
                            .setErrMsg(smsSingleSenderResult.errMsg)
                            .setTemplateId(smsConfig.getTemplateId());
                    smsRecordService.save(smsRecord);
                    throw new ServiceException(smsSingleSenderResult.errMsg, SystemCode.FAILURE_CODE);
                }
            } catch (Exception e) {
                throw new ServiceException(e.getMessage());
            }
        }
        //阿里
        if (SmsConstant.ALI.equals(smsConfig.getProviderName())) {
            JSONObject param = new JSONObject();
            param.set(CommonPool.CODE, smsSendDto.getSmsSendParam());
            try {
                Client client = createClient(smsConfig.getProviderAppId(), smsConfig.getProviderAppSecret());
                SendSmsRequest sendSmsRequest = new SendSmsRequest()
                        .setPhoneNumbers(smsSendDto.getSmsSendMobiles())
                        .setSignName(smsConfig.getSignature())
                        .setTemplateCode(smsConfig.getTemplateCode())
                        .setTemplateParam(param.toString());
                SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);
                log.warn("sendSmsResponse: " + sendSmsResponse);
                if (!"OK".equalsIgnoreCase(sendSmsResponse.getBody().getCode())) {
                    SmsRecord smsRecord = new SmsRecord();
                    smsRecord.setSmsSendMobiles(smsSendDto.getSmsSendMobiles())
                            .setSmsSendParam(smsSendDto.getSmsSendParam())
                            .setSignId(smsConfig.getSmsSignId())
                            .setIsErr(Boolean.TRUE)
                            .setErrMsg(sendSmsResponse.getBody().getMessage())
                            .setTemplateId(smsConfig.getTemplateId());
                    smsRecordService.save(smsRecord);
                    throw new ServiceException(sendSmsResponse.getBody().getMessage(), SystemCode.FAILURE_CODE);
                }
            } catch (Exception e) {
                log.error("阿里云短信发送异常", e);
                throw new ServiceException(e.getMessage(), SystemCode.FAILURE_CODE);
            }
        }
        SmsRecord smsRecord = new SmsRecord();
        smsRecord.setSmsSendMobiles(smsSendDto.getSmsSendMobiles())
                .setSmsSendParam(smsSendDto.getSmsSendParam())
                .setSignId(smsConfig.getSmsSignId())
                .setTemplateId(smsConfig.getTemplateId());
        boolean saveSmsRecord = smsRecordService.save(smsRecord);
        if (!saveSmsRecord) {
            throw new ServiceException("短信记录保存失败", SystemCode.DATA_ADD_FAILED_CODE);
        }

    }

    /**
     * 新增或修改短信签名
     *
     * @param smsSign 参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAndEditSmsSign(SmsSign smsSign) {
        if (smsSign.getId() != null) {
            boolean update = this.lambdaUpdate().update(smsSign);
            if (!update) {
                throw new ServiceException("短信签名更新失败!", SystemCode.DATA_UPDATE_FAILED_CODE);
            }
            return;
        }
        boolean save = this.save(smsSign);
        if (!save) {
            throw new ServiceException("短信签名更新失败!", SystemCode.DATA_ADD_FAILED_CODE);
        }
    }

    /**
     * 根据type 获取短信配置信息
     *
     * @param type 短信平台类型
     * @return 短信配置信息
     */
    @Override
    public SmsConfigDto smsConfig(StorageType type) {
        String providerName = type.getValue().equals(StorageType.ALIYUN.getValue()) ? SmsConstant.ALI : SmsConstant.TENCENT;
        SmsSign smsSign = this.lambdaQuery().eq(SmsSign::getProviderName, providerName).one();
        if (BeanUtil.isEmpty(smsSign)) {
            return new SmsConfigDto();
        }
        List<SmsTemplate> list = smsTemplateService.lambdaQuery()
                .eq(SmsTemplate::getSmsTemplateType, SmsTemplateType.CAPTCHA)
                .eq(SmsTemplate::getSmsSignId, smsSign.getId())
                .list();
        SmsConfigDto smsConfigDto = new SmsConfigDto();
        BeanUtil.copyProperties(smsSign, smsConfigDto);
        smsConfigDto.setSmsTemplates(list);
        return smsConfigDto;
    }


    @Override
    public void saveAndEditSmsTemplate(SmsTemplate smsTemplate) {
        this.lambdaQuery().eq(SmsSign::getId, smsTemplate.getSmsSignId())
                .oneOpt()
                .orElseThrow(() -> new ServiceException("模板所属短信签名不存在!", SystemCode.DATA_NOT_EXIST_CODE));
        //修改短信模板
        if (smsTemplate.getId() != null) {
            boolean update = smsTemplateService.lambdaUpdate().update(smsTemplate);
            if (!update) {
                throw new ServiceException("短信模板更新失败!", SystemCode.DATA_UPDATE_FAILED_CODE);
            }
            return;
        }
        boolean save = smsTemplateService.save(smsTemplate);
        if (!save) {
            throw new ServiceException("短信模板添加失败!", SystemCode.DATA_ADD_FAILED_CODE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAndEditSmsConfig(SmsCurrentConfigDto smsCurrentConfigDto) {
        if (smsCurrentConfigDto.getType() == StorageType.ALIYUN) {
            smsCurrentConfigDto.setProviderName(SmsConstant.ALI);
        } else if (smsCurrentConfigDto.getType() == StorageType.TENCENT) {
            smsCurrentConfigDto.setProviderName(SmsConstant.TENCENT);
        }
        if (smsCurrentConfigDto.getId() != null && smsCurrentConfigDto.getSmsTemplate().getId() != null) {
            SmsSign smsSign = getById(smsCurrentConfigDto.getId());
            if (BeanUtil.isEmpty(smsSign)) {
                throw new ServiceException("短信签名不存在", SystemCode.DATA_NOT_EXIST_CODE);
            }
            SmsTemplate smsTemplate = smsTemplateService.getById(smsCurrentConfigDto.getSmsTemplate().getId());
            if (BeanUtil.isEmpty(smsTemplate)) {
                throw new ServiceException("短信模板不存在", SystemCode.DATA_NOT_EXIST_CODE);
            }
            BeanUtil.copyProperties(smsCurrentConfigDto, smsSign);
            if (!updateById(smsSign)) {
                throw new ServiceException("短信签名修改失败!", SystemCode.DATA_UPDATE_FAILED_CODE);
            }
            BeanUtil.copyProperties(smsCurrentConfigDto.getSmsTemplate(), smsTemplate);
            if (!smsTemplateService.updateById(smsTemplate)) {
                throw new ServiceException("短信模板修改失败!", SystemCode.DATA_UPDATE_FAILED_CODE);
            }
            return;
        }
        SmsSign smsSign = new SmsSign();
        BeanUtil.copyProperties(smsCurrentConfigDto, smsSign);
        if (!save(smsSign)) {
            throw new ServiceException("短信签名保存失败", SystemCode.DATA_ADD_FAILED_CODE);
        }
        SmsTemplate smsTemplate = new SmsTemplate();
        BeanUtil.copyProperties(smsCurrentConfigDto.getSmsTemplate(), smsTemplate);
        smsTemplate.setSmsSignId(smsSign.getId());
        if (!smsTemplateService.save(smsTemplate)) {
            throw new ServiceException("短信模板保存失败", SystemCode.DATA_ADD_FAILED_CODE);
        }

    }

    /**
     * 阿里云配置初始化 无需改动
     *
     * @param accessKeyId     accessKeyId
     * @param accessKeySecret accessKeySecret
     * @return com.aliyun.dysmsapi20170525.Client
     * @throws Exception e
     */
    public Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        Config config = new Config()
                // AccessKey ID
                .setAccessKeyId(accessKeyId)
                // AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = "dysmsapi.aliyuncs.com";
        return new com.aliyun.dysmsapi20170525.Client(config);
    }
}
