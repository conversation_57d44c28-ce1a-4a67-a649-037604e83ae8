package com.medusa.gruul.carrier.pigeon.service.im.service.impl;

import com.medusa.gruul.carrier.pigeon.service.im.entity.UserInfo;
import com.medusa.gruul.carrier.pigeon.service.im.repository.UserInfoRepository;
import com.medusa.gruul.carrier.pigeon.service.im.service.CarrierUserService;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.service.uaa.api.rpc.UaaRpcService;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>信鸽用户接口实现</p>
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class CarrierUserServiceImpl implements CarrierUserService {

    private final UserInfoRepository userInfoRepository;
    private final UaaRpcService uaaRpcService;

    /**
     * 尝试从Redis中获取用户信息,若不存在则从RPC拉取并缓存到Redis.
     *
     * @param userId 用户ID
     * @return {@link UserInfo}
     */
    @Override
    public UserInfo checkAndGetUserInfo(Long userId) {
        return Option
                .of(userInfoRepository.getUserById(userId))
                .getOrElse(
                        () ->
                                uaaRpcService.getUserDataByUserId(userId)
                                        .map(
                                                user -> {
                                                    UserInfo userInfo = new UserInfo(userId, user.getNickname(), user.getAvatar());
                                                    userInfoRepository.saveUser(new UserInfo(userId, user.getNickname(), user.getAvatar()));
                                                    return userInfo;
                                                }
                                        )
                                        .getOrElseThrow(() -> new ServiceException("用户不存在"))
                );
    }
}
