package com.medusa.gruul.carrier.pigeon.service.modules.sms.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.carrier.pigeon.api.sms.dto.SmsRecordDto;
import com.medusa.gruul.carrier.pigeon.api.sms.entity.SmsRecord;
import com.medusa.gruul.carrier.pigeon.api.sms.vo.SmsRecordVo;
import com.medusa.gruul.carrier.pigeon.service.modules.sms.mapper.SmsRecordMapper;
import com.medusa.gruul.carrier.pigeon.service.modules.sms.service.SmsRecordService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/12/12
 */
@Service
public class SmsRecordServiceImpl extends ServiceImpl<SmsRecordMapper, SmsRecord> implements SmsRecordService {
    /**
     * 查询短信记录
     *
     * @param smsRecordDto
     * @return
     */
    @Override
    public IPage<SmsRecordVo> getSmsRecord(SmsRecordDto smsRecordDto) {
        return this.baseMapper.getSmsRecord(smsRecordDto);
    }
}
