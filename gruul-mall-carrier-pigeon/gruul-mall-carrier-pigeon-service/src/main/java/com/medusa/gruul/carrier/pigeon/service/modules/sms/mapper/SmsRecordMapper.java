package com.medusa.gruul.carrier.pigeon.service.modules.sms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.carrier.pigeon.api.sms.dto.SmsRecordDto;
import com.medusa.gruul.carrier.pigeon.api.sms.entity.SmsRecord;
import com.medusa.gruul.carrier.pigeon.api.sms.vo.SmsRecordVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2022/12/12
 */
public interface SmsRecordMapper extends BaseMapper<SmsRecord> {
    /**
     * 查询短信记录
     *
     * @param smsRecordDto
     * @return
     */
    IPage<SmsRecordVo> getSmsRecord(@Param("smsRecordDto") SmsRecordDto smsRecordDto);
}
