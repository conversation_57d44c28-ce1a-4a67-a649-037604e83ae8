package com.medusa.gruul.carrier.pigeon.service.modules.oss.cloud;

import com.medusa.gruul.carrier.pigeon.api.oss.model.dto.OssConfigDto;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.obs.services.ObsClient;
import com.obs.services.exception.ObsException;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * @Description: 华为云存储
 * @Author: xiaoq
 * @Date : 2022-04-11 17:50
 */
public class QuidwayCloundStorageService extends AbstractCloudStorageService {

    private ObsClient obsClient;

    public QuidwayCloundStorageService(OssConfigDto config) {
        this.config = config;
        //初始化
        init();
    }

    private void init() {
        obsClient = new ObsClient(
                config.getQuidwayAccessKeyId(), config.getQuidwayAccessKeySecret(), config.getQuidwayEndpoint()
        );
    }


    /**
     * 文件上传
     *
     * @param data 文件字节数组
     * @param path 文件路径，包含文件名
     * @return 返回http地址
     */
    @Override
    public String upload(byte[] data, String path) {
        return upload(new ByteArrayInputStream(data), path);
    }


    /**
     * 文件上传
     *
     * @param inputStream 字节流
     * @param path        文件路径，包含文件名
     * @return 返回http地址
     */
    @Override
    public String upload(InputStream inputStream, String path) {
        try {
            obsClient.putObject(config.getObsBucketName(), path, inputStream);
        } catch (ObsException e) {
            e.printStackTrace();
            throw new ServiceException("上传文件失败，请检查配置信息", e);
        }

        return config.getQuidwayDomain() + "/" + path;
    }

    /**
     * 文件上传
     *
     * @param inputStream 字节流
     * @param suffix      后缀
     * @return 返回http地址
     */
    @Override
    public String uploadSuffix(InputStream inputStream, String suffix) {
        return upload(inputStream, getPath(config.getQuidwayPrefix(), suffix));
    }

    /**
     * 文件上传
     *
     * @param data   文件字节数组
     * @param suffix 后缀
     * @return 返回http地址
     */
    @Override
    public String uploadSuffix(byte[] data, String suffix) {
        return upload(data, getPath(config.getQuidwayPrefix(), suffix));
    }

}
