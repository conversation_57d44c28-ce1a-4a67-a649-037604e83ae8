package com.medusa.gruul.carrier.pigeon.service.modules.sms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.carrier.pigeon.api.sms.dto.EditSmsConfigDto;
import com.medusa.gruul.carrier.pigeon.api.sms.entity.SmsSign;
import com.medusa.gruul.carrier.pigeon.api.sms.enums.SmsTemplateType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/9
 */
public interface SmsSignMapper extends BaseMapper<SmsSign> {
    /**
     * 根据服务商与短信模板获取短信配置
     *
     * @param templateType
     * @param providerName
     * @return
     */
    List<EditSmsConfigDto> getSmsConfig(@Param("templateType") SmsTemplateType templateType, @Param("providerName") String providerName);
}
