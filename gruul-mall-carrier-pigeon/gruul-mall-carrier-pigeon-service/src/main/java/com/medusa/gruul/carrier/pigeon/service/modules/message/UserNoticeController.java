package com.medusa.gruul.carrier.pigeon.service.modules.message;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.carrier.pigeon.service.model.dto.NoticePageDTO;
import com.medusa.gruul.carrier.pigeon.service.mp.entity.PigeonMessage;
import com.medusa.gruul.carrier.pigeon.service.service.NoticeService;
import com.medusa.gruul.common.model.resp.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公告消息
 */
@RestController
@RequestMapping("/pigeon/notice/consumer")
@RequiredArgsConstructor
public class UserNoticeController {
    private final NoticeService noticeService;

    /**
     * 用户端获取公告通知列表
     * @param noticePage
     * @return
     */
    @GetMapping("/list")
    public Result<IPage<PigeonMessage>> pageNoticePlatform(NoticePageDTO noticePage) {
        return Result.ok(
                noticeService.pageNoticeConsumer(noticePage)
        );
    }
}
