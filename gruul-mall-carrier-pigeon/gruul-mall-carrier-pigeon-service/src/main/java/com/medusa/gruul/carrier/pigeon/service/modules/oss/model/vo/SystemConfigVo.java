package com.medusa.gruul.carrier.pigeon.service.modules.oss.model.vo;

import com.medusa.gruul.carrier.pigeon.service.modules.oss.config.AliyunStorageConfig;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.config.QiniouStorageConfig;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.config.QuidwayStorageConfig;
import com.medusa.gruul.carrier.pigeon.service.modules.oss.config.TencentStorageConfig;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SystemConfigVo {

    /**
     * 阿里云配置
     */
    private AliyunStorageConfig aliyunStorageConfig;
    /**
     * 七牛云配置
     */
    private QiniouStorageConfig qiniouStorageConfig;
    /**
     * 腾讯云配置
     */
    private TencentStorageConfig tencentStorageConfig;
    /**
     * 华为云配置
     */
    private QuidwayStorageConfig quidwayStorageConfig;

}
