package com.medusa.gruul.carrier.pigeon.service.mq;

/**
 * <AUTHOR>
 * date 2022/10/19
 */

public interface PigeonRabbitQueueNames {

    /**
     * 订单一支付 队列
     */
    String PIGEON_ORDER_PAID_BROADCAST_QUEUE = "pigeon.order.paid.broadcast";

    /**
     * 店铺信息修改
     */
    String PIGEON_SHOP_INFO_UPDATE_QUEUE = "pigeon.shop.info.update";

    /**
     * 小程序订阅消息
     */
    String PIGEON_APPLET_SUBSCRIBE_QUEUE = "pigeon.applet.subscribe";

    /**
     * 邮件消息
     */
    String PIGEON_EMAIL_MESSAGE_QUEUE = "pigeon.email.message";
}
