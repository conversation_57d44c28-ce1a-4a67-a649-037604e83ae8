package com.medusa.gruul.carrier.pigeon.service.modules.sms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.carrier.pigeon.api.sms.dto.SmsConfigDto;
import com.medusa.gruul.carrier.pigeon.api.sms.dto.SmsCurrentConfigDto;
import com.medusa.gruul.carrier.pigeon.api.sms.dto.SmsSendDto;
import com.medusa.gruul.carrier.pigeon.api.sms.entity.SmsSign;
import com.medusa.gruul.carrier.pigeon.api.sms.entity.SmsTemplate;
import com.medusa.gruul.carrier.pigeon.api.sms.enums.StorageType;

/**
 * <AUTHOR>
 * @date 2022/12/9
 */
public interface SmsSignService extends IService<SmsSign> {
    /**
     * 发送短信
     *
     * @param msg 参数
     */
    void smsSend(SmsSendDto msg);

    /**
     * 新增或修改短信签名
     *
     * @param smsSign 参数
     */
    void saveAndEditSmsSign(SmsSign smsSign);

    /**
     * 根据type 获取短信配置信息
     *
     * @param type
     * @return
     */
    SmsConfigDto smsConfig(StorageType type);

    /**
     * 新增或修改短信模板
     *
     * @param smsTemplate
     */
    void saveAndEditSmsTemplate(SmsTemplate smsTemplate);

    /**
     * 保存短信配置 【过渡使用】
     *
     * @param smsCurrentConfigDto
     */
    void saveAndEditSmsConfig(SmsCurrentConfigDto smsCurrentConfigDto);
}
