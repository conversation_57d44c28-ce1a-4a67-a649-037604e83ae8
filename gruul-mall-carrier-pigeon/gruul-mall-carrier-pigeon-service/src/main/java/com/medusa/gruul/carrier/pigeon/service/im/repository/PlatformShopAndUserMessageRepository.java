package com.medusa.gruul.carrier.pigeon.service.im.repository;


import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.carrier.pigeon.api.enums.UserType;
import com.medusa.gruul.carrier.pigeon.service.im.entity.ChatMessage;
import com.medusa.gruul.carrier.pigeon.service.im.entity.GroupChatRoom;
import com.medusa.gruul.carrier.pigeon.service.im.entity.PlatformShopAndUserChatRoom;
import com.medusa.gruul.carrier.pigeon.service.im.model.dto.PlatformShopAndUserSingeRoomQueryDTO;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 基于Redis实现的平台/店铺-用户消息持久层
 *
 * <AUTHOR>
 */
@Repository
@Slf4j
@RequiredArgsConstructor
public class PlatformShopAndUserMessageRepository {

    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 获取指定聊天室的消息
     *
     * @param chatRoom {@link PlatformShopAndUserChatRoom}
     * @param start    消息起始索引
     * @param end      消息结束索引
     * @return {@link Set}
     */
    public Set<String> listChatMessageByRoomId(PlatformShopAndUserChatRoom chatRoom, int start, int end) {
        return stringRedisTemplate.opsForZSet().reverseRange(chatRoom.getRoomId(), start, end);
    }

    /**
     * 根据分页信息,获取指定的聊天室的消息记录
     *
     * @param chatRoom 聊天室对象,参见{@link GroupChatRoom}
     * @param dto      分页对象
     * @return {@link ChatMessage}
     */
    public IPage<ChatMessage> listMessageByRoomId(PlatformShopAndUserChatRoom chatRoom, PlatformShopAndUserSingeRoomQueryDTO dto) {
        log.info("获取群聊天室聊天记录, param = {}", JSON.toJSONString(dto));
        Long total = stringRedisTemplate.opsForZSet().size(chatRoom.getRoomId());
        if (total <= 0) {
            log.warn("聊天室 = {}, 不存在或暂无聊天记录");
            return new Page<>(0, 0);
        }
        long start = (dto.getCurrent() - 1) * dto.getSize();
        long end = dto.getCurrent() * dto.getSize();
        IPage<ChatMessage> result = new Page<>(dto.getCurrent(), dto.getSize(), total);
        Set<String> msgObjects = stringRedisTemplate.opsForZSet().reverseRange(chatRoom.getRoomId(), start, end);
        result.setRecords(msgObjects
                .stream()
                .map(e -> JSON.parseObject(String.valueOf(e), ChatMessage.class))
                .collect(Collectors.toList()));
        this.updateMessageRead(chatRoom, result.getRecords());
        return result;
    }

    /**
     * 批量更新消息已读
     *
     * @param chatRoom     {@link PlatformShopAndUserChatRoom}
     * @param chatMessages {@link  ChatMessage}
     */
    public void updateMessageRead(PlatformShopAndUserChatRoom chatRoom, List<ChatMessage> chatMessages) {
        // 如果当前请求账号为接收方，更新消息状态已读
        SecureUser secureUser = ISecurity.userMust();
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection stringRedisConn = (StringRedisConnection) connection;
            for (ChatMessage message : chatMessages) {
                if (message.getReceiver().getReceiverType().equals(UserType.CONSUMER)
                        && message.getReceiver().getReceiverUserInfo().getUserId().equals(secureUser.getId())
                        || message.getReceiver().getReceiverType().equals(UserType.SHOP_ADMIN)
                        && message.getReceiver().getReceiverShopInfo().getShopId().equals(String.valueOf(secureUser.getShopId()))){
                    stringRedisConn.zRem(chatRoom.getRoomId(), JSON.toJSONString(message));
                    message.setRead(Boolean.TRUE);
                    stringRedisConn.zAdd(chatRoom.getRoomId(), message.getSendTime(), JSON.toJSONString(message));
                }
            }
            return null;
        });
    }

    /**
     * 发送消息到指定聊天室
     *
     * @param destination {@link PlatformShopAndUserChatRoom}
     * @param chatMessage {@link ChatMessage}
     */
    public void sendMessage(PlatformShopAndUserChatRoom destination, ChatMessage chatMessage) {
        stringRedisTemplate.opsForZSet()
                .add(destination.getRoomId(), JSON.toJSONString(chatMessage), chatMessage.getSendTime());
    }
}
