<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.carrier.pigeon.service.mp.mapper.MessageShopMapper">

    <resultMap id="messageShopPageMap" type="com.medusa.gruul.carrier.pigeon.service.model.vo.MessageShopVO">
        <id column="id" property="id"/>
        <result column="shopId" property="shopId"/>
        <result column="shopName" property="shopName"/>
        <result column="shopLogo" property="shopLogo"/>
        <result column="lastTime" property="lastTime"/>
        <result column="showNotice" property="show"/>
        <association property="lastMessage" javaType="com.medusa.gruul.carrier.pigeon.service.mp.entity.Message">
            <id column="msgId" property="id"/>
            <result column="msgShopId" property="shopId"/>
            <result column="msgAdminId" property="adminId"/>
            <result column="msgUserId" property="userId"/>
            <result column="msgSenderType" property="senderType"/>
            <result column="msgReceiverType" property="receiverType"/>
            <result column="msgMessageType" property="messageType"/>
            <result column="msgMessage" property="message"/>
            <result column="msgRead" property="read"/>
            <result column="msgCreateTime" property="createTime"/>
        </association>
    </resultMap>
    <select id="messageShopPage" resultMap="messageShopPageMap">
        SELECT
        msgShop.id AS id,
        msgShop.shop_id AS shopId,
        msgShop.shop_name AS shopName,
        msgShop.shop_logo AS shopLogo,
        message.create_time AS lastTime,
        ( message.sender_type = ${@com.medusa.gruul.carrier.pigeon.api.enums.UserType @SHOP_ADMIN.value} AND
        message.`read` = 0 ) AS showNotice,
        message.id AS msgId,
        message.shop_id AS msgShopId,
        message.admin_id AS msgAdminId,
        message.user_id AS msgUserId,
        message.sender_type AS msgSenderType,
        message.receiver_type AS msgReceiverType,
        message.message_type AS msgMessageType,
        message.message AS msgMessage,
        message.`read` AS msgRead,
        message.create_time AS msgCreateTime
        FROM t_message_shop AS msgShop
        INNER JOIN ( SELECT temp2.* FROM t_message AS temp2 WHERE temp2.id IN ( ( SELECT MAX( temp1.id ) FROM t_message
        AS temp1 WHERE temp1.user_id = #{query.userId} GROUP BY temp1.shop_id ) ) ) AS message ON message.shop_id =
        msgShop.shop_id
        WHERE msgShop.deleted = 0
        AND msgShop.shop_id != 0

        <if test="query.keywords != null and query.keywords !=''">
            AND msgShop.shop_name LIKE CONCAT('%',#{query.keywords},'%')
        </if>

        ORDER BY showNotice DESC, lastTime DESC

    </select>
</mapper>
