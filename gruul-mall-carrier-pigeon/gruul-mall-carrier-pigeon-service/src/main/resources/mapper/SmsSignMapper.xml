<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.medusa.gruul.carrier.pigeon.service.modules.sms.mapper.SmsSignMapper">

    <resultMap id="BaseDtoResultMap" type="com.medusa.gruul.carrier.pigeon.api.sms.dto.EditSmsConfigDto">
        <result column="provider_app_id" property="providerAppId"/>
        <result column="provider_app_secret" property="providerAppSecret"/>
        <result column="provider_name" property="providerName"/>
        <result column="signature" property="signature"/>
        <result column="template_code" property="templateCode"/>
        <result column="sms_sign_id" property="smsSignId"/>
        <result column="sms_template_id" property="templateId"/>
    </resultMap>


    <select id="getSmsConfig" resultMap="BaseDtoResultMap">
    SELECT
        smsSign.provider_name,
        smsSign.provider_app_id,
        smsSign.provider_app_secret,
        smsSign.signature,
        smsTemplate.template_code,
        smsSign.id AS sms_sign_id,
        smsTemplate.id AS sms_template_id
        FROM
            t_sms_sign AS smsSign
        INNER JOIN t_sms_template AS smsTemplate on smsSign.id=smsTemplate.sms_sign_id AND smsTemplate.deleted=0
        WHERE
           smsSign.provider_name = #{providerName}
          <choose>
              <when test="templateType ==@com.medusa.gruul.carrier.pigeon.api.sms.enums.SmsTemplateType @CAPTCHA">
                  AND smsTemplate.type=${@com.medusa.gruul.carrier.pigeon.api.sms.enums.SmsTemplateType @CAPTCHA.value}
              </when>
          </choose> 
        AND smsSign.deleted = 0
        ORDER BY smsSign.update_time DESC
    </select>
</mapper>