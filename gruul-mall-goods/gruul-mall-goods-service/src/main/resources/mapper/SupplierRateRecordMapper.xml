<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.goods.service.mp.mapper.SupplierRateRecordMapper">

    <resultMap id="BaseResultMap" type="com.medusa.gruul.goods.api.entity.SupplierRateRecord">
        <result column="rate" property="rate"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="evaluateNum" property="evaluateNum"/>
    </resultMap>

    <select id="querySupplierRealRate" resultType="com.medusa.gruul.goods.api.entity.SupplierRateRecord">
        SELECT
            COUNT(id) AS evaluateNum,
            SUM(rate) AS rate,
            supplier_id
        FROM
            t_supplier_rate_record
        WHERE
	        supplier_id IN
        <foreach collection="supperIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY  supplier_id

    </select>
</mapper>