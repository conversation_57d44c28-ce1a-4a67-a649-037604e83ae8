<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.goods.service.mp.mapper.SupplierProductSkuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.goods.api.model.vo.SupplierProductSkuVO">
        <result column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="supplier_product_id" property="supplierProductId"/>
        <result column="storage_sku_id" property="storageSkuId"/>
        <result column="product_no" property="productNo"/>
        <result column="product_info" property="productInfo"/>
    </resultMap>

    <!-- 通用查询结果列 -->
<!--    <sql id="Base_Column_List">-->
<!--        id, create_time, supplier_sn, `name`, mobile, province,city,country,address,address_code,area,product_info,-->
<!--	STATUS,score-->
<!--    </sql>-->
    <select id="getSupplierSkuByProductId" resultMap="BaseResultMap">
        SELECT
            productSku.*,
            product.product_no,
            product.product_info,
            product.`name`
        FROM
            `t_supplier_product_sku` productSku
        LEFT JOIN t_supplier_product product ON productSku.supplier_product_id = product.id
        <where>
            productSku.deleted = 0
            <if test="productId!=null">
                AND productSku.product_id = #{productId}
            </if>
        </where>
        ORDER BY
        create_time
        DESC
    </select>

    <select id="getSupplierSkuBySkuId" resultMap="BaseResultMap">
        SELECT
        productSku.*,
        product.product_no,
        product.product_info,
        product.`name`
        FROM
        `t_supplier_product_sku` productSku
        LEFT JOIN t_supplier_product product ON productSku.supplier_product_id = product.id
        <where>
            productSku.deleted = 0
            <if test="skuId!=null">
                AND productSku.storage_sku_id = #{skuId}
            </if>
        </where>
    </select>


</mapper>
