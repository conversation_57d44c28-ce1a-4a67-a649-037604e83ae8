goods.virtual.distribution.check.error=Virtual shipping verification failed
goods.real.distribution.check.error=Physical Delivery Verification Failed
goods.current.info.not.exist=The current information does not exist
goods.product.feature.deleted.fail=Description Failed to delete the product feature information
goods.parameter.error.check.required=Parameter error, please check required fields
goods.followed.this.shop=You have followed this store
goods.current.goods.not.exist=The current item does not exist
goods.attributes.repetition=Do not pass duplicate attributes
goods.current.goods.attributes.not.exist=The current product attribute does not exist
goods.product.attributes.have.changed=Product properties have changed. Please try again later
goods.classify.unable.delete=The category has bound goods and cannot be deleted
goods.supplier.audit.fail=Supplier audit failure
goods.supplier.have.delete=The supplier has been removed
goods.supplier.add.fail=Supplier addition failure
goods.supplier.update.fail=Supplier update failure
goods.phone.already.supplier=The phone number is already the supplier
goods.already.supplier.but.forbidden=The phone number is already a vendor, but is disabled!
goods.product.issue.fail=Product release failure
goods.product.delete.fail=Product delete failure
goods.product.update.fail=Product update failure
goods.link.error=Link error, please check the link
goods.classify.update.fail=Description Failed to update the classification data
goods.classify.data.not.exist=Categorical data does not exist
goods.classify.name.repetition=The added category cannot be the same as the upper-level category
goods.sibling.classify.name.repetition=Under the same category, the same category already exists
goods.classify.save.fail=Description Failed to save the classification data
goods.status.operate.exception=Platform products can not be placed on the shelf, if you need to be placed, please contact the administrator
goods.category.img.not.exist=categoryImg cannot be empty
goods.category.bmp.fail=categoryImg does not comply with format requirements
goods.set.sibling.classify.name.repetition=The current set category name is duplicate please check, category name: {0}
goods.product.attributes.required.check.fail={0} Be required
goods.product.attributes.radio.check.fail={0} Be radio
goods.api.exception.99api={0} {1}
goods.product.cannot.be.deleted=The currently deleted products exist unremoved products and cannot be deleted
goods.current.product.not.available=Current item cannot be listed
goods.supplier.invalid=Supplier not available
goods.current.consignment.product.not.exist.or.exception=The current consignment does not exist or has been removed in violation of regulations
