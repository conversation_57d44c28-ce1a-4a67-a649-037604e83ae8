goods.virtual.distribution.check.error=虚拟配送校验失败
goods.real.distribution.check.error=实物配送校验失败
goods.current.info.not.exist=当前信息不存在
goods.product.feature.deleted.fail=产品特性信息删除失败
goods.parameter.error.check.required=参数错误,请检查必填项
goods.followed.this.shop=您已关注该店铺
goods.current.goods.not.exist=当前商品不存在
goods.attributes.repetition=请勿传递重复属性
goods.current.goods.attributes.not.exist=当前产品属性不存在
goods.product.attributes.have.changed=产品属性发生变化,请稍后再试
goods.classify.unable.delete=该分类存在绑定商品,不能删除
goods.supplier.audit.fail=供应商审核失败
goods.supplier.have.delete=供应商已被删除
goods.supplier.add.fail=供应商新增失败
goods.supplier.update.fail=供应商更新失败
goods.phone.already.supplier=该手机号已是供应商
goods.already.supplier.but.forbidden=该手机号已是供应商,但被禁用！
goods.product.issue.fail=商品发布失败
goods.product.delete.fail=商品删除失败
goods.product.update.fail=商品修改失败
goods.link.error=链接错误，请检查链接
goods.classify.update.fail=分类更新数据失败
goods.classify.data.not.exist=分类数据不存在
goods.classify.name.repetition=添加分类不可与上级分类同名
goods.sibling.classify.name.repetition=同级分类下,已存在同名分类
goods.classify.save.fail=分类保存数据失败
goods.status.operate.exception=平台下架商品不可上架,如需上架请联系管理员
goods.category.img.not.exist=categoryImg不能为空
goods.category.bmp.fail=categoryImg不符合格式要求
goods.set.sibling.classify.name.repetition=当前设置类目名称存在重复请检查,类目名称: {0}
goods.product.attributes.required.check.fail={0} 为必选项
goods.product.attributes.radio.check.fail={0} 为单选
goods.api.exception.99api={0} {1}
goods.product.cannot.be.deleted=当前删除商品存在未下架商品,不可删除
goods.current.product.not.available=当前商品不可上架
goods.supplier.invalid=供应商不可用
goods.current.consignment.product.not.exist.or.exception=当前代销商品不存在,或已被违规下架
