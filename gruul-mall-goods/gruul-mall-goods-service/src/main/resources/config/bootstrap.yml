server:
  port: 9104
spring:
  main:
    allow-circular-references: true
  application:
    name: gruul-mall-goods
  profiles:
    active: prod
  cloud:
    nacos:
      server-addr: **************:8884
      discovery:
        namespace: ${spring.profiles.active}
        ip: *************
      config:
        namespace: ${spring.profiles.active}
        file-extension: yml
        shared-configs:
          - dataId: application-common.${spring.cloud.nacos.config.file-extension}
# 暴露端点
management:
  endpoints:
    web:
      exposure:
        include: '*'  # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 *, 可以开放所有端点
  endpoint:
    health:
      show-details: always