package com.medusa.gruul.goods.service.controller.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.goods.api.entity.ProductTag;
import com.medusa.gruul.goods.api.model.dto.ProductTagDTO;
import com.medusa.gruul.goods.service.mp.service.IProductTagService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@RestController
@RequestMapping("manager/productTag")
@RequiredArgsConstructor
public class ProductTagController {
    private final IProductTagService productTagService;


    /**
     * 获取产品标签列表
     *
     * @param productTagDTO
     * @return
     */
    @GetMapping("list")
    public Result<IPage<ProductTag>> getProductTagByParam(ProductTagDTO productTagDTO) {
        SecureUser secureUser = ISecurity.secureUser();
        productTagDTO.setShopId(secureUser.getShopId());
        return Result.ok(productTagService.queryProductTagByParam(productTagDTO));
    }

    /**
     * 更新产品标签数据
     *
     * @param productTagDTO
     * @return
     */
    @PutMapping("update")
    public Result updateProductTag(@RequestBody ProductTagDTO productTagDTO) {

        productTagService.updateProductTag(productTagDTO);
        return Result.ok();

    }

    /**
     * 修改产品标签数据
     *
     * @param productTagDTO
     * @return
     */
    @PostMapping("save")
    private Result saveProductTag(@RequestBody ProductTagDTO productTagDTO) {
        productTagService.saveProductTag(productTagDTO);
        return Result.ok();
    }

    /**
     * 删除产品标签
     *
     * @param ids
     * @return
     */
    @DeleteMapping("delete")
    public Result deleteProductTag(@RequestBody Set<Long> ids) {
        productTagService.deleteProductTag(ids);
        return Result.ok();
    }
}
