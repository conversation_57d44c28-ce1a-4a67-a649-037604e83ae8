package com.medusa.gruul.goods.service.service;

import com.medusa.gruul.goods.api.model.dto.ProductDeleteDTO;
import com.medusa.gruul.shop.api.model.dto.ShopProductSyncDTO;

/**
 *
 * 店铺产品同步 服务类
 *
 *
 * <AUTHOR>
 */
public interface ShopProductSyncService {

    /**
     * 同步店铺商品
     *
     * @param shopProduct 同步店铺商品
     */
    void shopProduct(ShopProductSyncDTO shopProduct);


    /**
     * 店铺商品批量删除
     *
     * @param productDelete 批量删除店铺商品
     */
    void productDelete(ProductDeleteDTO productDelete);
}
