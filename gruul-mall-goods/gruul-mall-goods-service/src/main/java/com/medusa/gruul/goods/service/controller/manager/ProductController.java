package com.medusa.gruul.goods.service.controller.manager;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.common.idem.annotation.Idem;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.goods.api.model.dto.ProductDTO;
import com.medusa.gruul.goods.api.model.dto.ProductStatusChangeDTO;
import com.medusa.gruul.goods.api.model.dto.QRCodeDTO;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import com.medusa.gruul.goods.api.model.param.ProductParam;
import com.medusa.gruul.goods.api.model.param.ProductStockParam;
import com.medusa.gruul.goods.api.model.vo.ProductStockVO;
import com.medusa.gruul.goods.api.model.vo.ProductVO;
import com.medusa.gruul.goods.service.model.dto.ConsignmentProductDTO;
import com.medusa.gruul.goods.service.model.param.PurchaseProductParam;
import com.medusa.gruul.goods.service.model.vo.SupplierIssueProductListVO;
import com.medusa.gruul.goods.service.mp.service.IProductService;
import com.medusa.gruul.goods.service.service.ShopProductSyncService;
import com.medusa.gruul.shop.api.model.dto.ShopProductSyncDTO;
import com.medusa.gruul.storage.api.vo.ProductSkusVO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Map;
import java.util.Set;


/**
 * 商品信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2022-03-04
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/manager/product")
@PreAuthorize("@S.shopPerm('goods:list')")
public class ProductController {

    private final IProductService productService;
    private final ShopProductSyncService shopProductSyncService;

    /**
     * 商品发布(新增)
     *
     * @param productDto 商品信息dto
     */
    @Log("商品发布")
    @Idem(1000)
    @PostMapping("/issue")
    public Result<Void> issueProduct(@RequestBody @Valid ProductDTO productDto) {
        productService.issueProduct(productDto);
        return Result.ok();
    }

    /**
     * 生成二维码
     *
     * @param qrCodeDTO 商品二维码DTO
     */
    @Log("生成二维码")
    @Idem(1000)
    @PostMapping("/qRCode")
    public void generateQRCoded(@RequestBody @Valid QRCodeDTO qrCodeDTO, HttpServletResponse response) {
         productService.generateQRCoded(qrCodeDTO,response);
    }
    /**
     * 商品信息删除
     *
     * @param ids 商品id
     */
    @Log("商品信息删除")
    @DeleteMapping("/delete/{ids}")
    public Result<Void> deleteProductList(@PathVariable(name = "ids") Set<Long> ids) {
        productService.deleteProductList(ids);
        return Result.ok();
    }


    /**
     * 商品信息修改
     *
     * @param productDto 商品信息DTO
     */
    @Log("商品信息修改")
    @Idem(1000)
    @PutMapping("/update")
    public Result<Void> updateProduct(@RequestBody @Validated ProductDTO productDto) {
        productService.updateProduct(productDto);
        return Result.ok();
    }


    /**
     * 商品信息列表
     *
     * @param productParam 商品查询param
     * @return IPage<商品信息VO>
     */
    @Log("商品信息列表")
    @GetMapping("/list")
    @PreAuthorize("@S.anyPerm('commodity','goods:list')")
    public Result<IPage<ProductVO>> getProductList(ProductParam productParam) {
        return Result.ok( productService.getProductList(productParam));
    }


    /**
     * 获取单个商品基础信息
     *
     * @param shopId  店铺id
     * @param id      商品id
     * @param request HttpServletRequest
     * @return 商品信息Vo
     */
    @Log("单个商品信息获取By id")
    @GetMapping("/get/{shopId}/{id}")
    public Result<ProductVO> getProductById(@PathVariable("shopId") Long shopId, @PathVariable("id") Long id, HttpServletRequest request) {
        ProductVO productVo = productService.getProductById(id, shopId, request.getRequestURI().contains("api/product/get"));
        return Result.ok(productVo);
    }


    /**
     * 商品上下架
     *
     * @param productStatusChange 商品状态更改信息
     * @param status              商品状态值
     */
    @Log("商品上下架")
    @Idem(500)
    @PutMapping("/updateStatus/{status}")
    @PreAuthorize("@S.anyPerm('commodity','goods:list')")
    public Result<Void> updateProductStatus(@RequestBody @Valid ProductStatusChangeDTO productStatusChange,
                                            @PathVariable("status") ProductStatus status) {
        productService.updateProductStatus(productStatusChange, status);
        return Result.ok();
    }


    /**
     * 平台获取商品数量/违规商品数量
     *
     * @return <map <ProductStatus,数量>>
     */
    @Log("平台获取商品数量/违规商品数量")
    @GetMapping("/quantity")
    @PreAuthorize("""
            		@S.matcher().any(@S.ROLES,@S.PLATFORM_ADMIN).
            		or(@S.consumer().eq(@S.ROLES,@S.PLATFORM_CUSTOM_ADMIN).eq(@S.PERMS,'overview'))
            		.match()
            """)
    public Result<Map<ProductStatus, Long>> getGoodsQuantity() {
        return Result.ok(productService.getGoodsQuantity());
    }


    /**
     * 获取今日新增商品数量
     *
     * @return 今日新增商品数量
     */
    @Log("获取今日新增商品数量")
    @GetMapping("/today/quantity")
    @PreAuthorize("""
            		@S.matcher().any(@S.ROLES,@S.PLATFORM_ADMIN,@S.R.SUPPLIER_ADMIN,@S.SHOP_ADMIN).
            		or(@S.consumer().eq(@S.ROLES,@S.SHOP_CUSTOM_ADMIN).eq(@S.PERMS,'overview')).
            		or(@S.consumer().eq(@S.ROLES,@S.PLATFORM_CUSTOM_ADMIN).eq(@S.PERMS,'overview'))
            		.match()
            """)
    public Result<Long> getTodayAddGoodsQuantity() {
        return Result.ok(productService.getTodayAddGoodsQuantity());
    }

    /**
     * 分页获取商品和规格信息
     *
     * @param productParam 商品查询参数
     * @return 商品规格信息
     */
    @Log("分页获取商品和规格信息")
    @GetMapping("/getProductSkus")
    @PreAuthorize("@S.anyPerm('commodity','goods:list')")
    public Result<IPage<ProductSkusVO>> getProductSkus(ProductParam productParam) {
        return Result.ok(productService.getProductSkus(productParam));
    }


    /**
     * 商品名称修改
     *
     * @param id   商品id
     * @param name 商品name
     * @return void
     */
    @PutMapping("update/{id}/{name}")
    @PreAuthorize("@S.anyPerm('commodity','goods:list')")
    public Result<Void> updateProductName(@PathVariable("id") Long id, @Size(max = 32, message = "商品名称过长") @PathVariable(value = "name") String name) {
        productService.updateProductName(id, name);
        return Result.ok();
    }


    /**
     * 获取商品库存基础信息
     *
     * @param param 检索条件
     * @return IPage<ProductSkusVO>
     */
    @Log("获取商品库存基础信息")
    @GetMapping("/getProductStockBaseInfo")
    public Result<IPage<ProductStockVO>> getProductStockBaseInfo(ProductStockParam param) {
        if (param.getProductIds() != null) {
            param.setProductIdList(param.getProductIds().split(","));
        }
        return Result.ok(productService.getProductStockBaseInfo(param));
    }


    /**
     * 获取采购已发布的商品信息
     *
     * @param param 查询parmam
     * @return IPage<SupplierIssueProductListVO>
     */
    @Log("获取采购已发布的商品信息")
    @GetMapping("/purchase/issue/products")
    public Result<IPage<SupplierIssueProductListVO>> getPurchaseIssueProducts(PurchaseProductParam param) {
        return Result.ok(productService.getPurchaseIssueProducts(param));
    }


    /**
     * 采购已发布商品修改状态
     *
     * @param id 商品id
     * @return Result<Void>
     */
    @Idem(1000)
    @Log("采购已发布商品修改状态")
    @PutMapping("purchase/issue/product/updateStatus/{id}")
    public Result<Void> purchaseIssueProductUpdateStatus(@PathVariable("id") @NotNull Long id) {
        productService.purchaseIssueProductUpdateStatus(id);
        return Result.ok();
    }


    /**
     * 获取代销商品详情
     *
     * @param id 商品id
     * @return 代销商品详情
     */
    @Log("获取代销商品详情")
    @GetMapping("consignment/{id}")
    public Result<ProductVO> consignmentProductInfo(@PathVariable("id") @NotNull Long id) {
        return Result.ok(productService.getConsignmentProductInfo(id));
    }

    /**
     * 代销商品修改
     *
     * @param consignmentProduct 代销商品修改DTO
     * @return Result<Void>
     */
    @Log("代销商品修改")
    @PostMapping("consignment/update")
    public Result<Void> consignmentProductUpdate(@RequestBody @Validated ConsignmentProductDTO consignmentProduct) {
        productService.consignmentProductUpdate(consignmentProduct);
        return Result.ok();
    }

    /**
     * 同步产品到其他店铺
     *
     * @param shopProductSyncDTO 商品信息
     */
    @Log("同步产品到其他店铺")
    @Idem(500)
    @PostMapping("/shop/sync")
    @PreAuthorize("@S.anyPerm('commodity','goods:list')")
    public Result<Void> shopProductSync(@RequestBody @Valid ShopProductSyncDTO shopProductSyncDTO) {
        //暂时同步调用
        shopProductSyncService.shopProduct(shopProductSyncDTO);
        return Result.ok();
    }

}