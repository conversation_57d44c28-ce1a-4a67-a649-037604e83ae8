package com.medusa.gruul.goods.service.mq;

/**
 * <AUTHOR>
 * date 2022/12/5
 */
public interface GoodsRabbitQueueNames {

    /**
     * 店铺状态改变 启用禁用
     */
    String SHOP_CHANGE_QUEUE = "goods.shop.change.queue";

    /**
     * 订单完成
     */
    String ORDER_ACCOMPLISH_QUEUE = "goods.order.accomplish.queue";

    /**
     * 订单评价完成
     */
    String ORDER_COMMENT_ACCOMPLISH_QUEUE = "goods.order.comment.accomplish.queue";

    /**
     * 店鋪信息修改
     */
    String SHOP_INFO_UPDATE_QUEUE = "goods.shop.info.update.queue";

    /**
     * 供应商更新商品状态
     */
    String SUPPLIER_GOODS_UPDATE_STATUS_QUEUE = "supplier.goods.update.status.queue";

    /**
     * 强制删除商品
     */
    String SUPPLIER_FORCE_GOODS_STATUS_QUEUE = "supplier.force.goods.status.queue";

    /**
     * 供应商代销商品修改
     */
    String SUPPLIER_UPDATE_GOODS = "supplier.update.goods";
}
