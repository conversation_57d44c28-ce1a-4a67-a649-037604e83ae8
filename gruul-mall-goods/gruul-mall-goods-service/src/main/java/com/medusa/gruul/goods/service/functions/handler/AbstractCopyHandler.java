package com.medusa.gruul.goods.service.functions.handler;

import com.medusa.gruul.common.web.handler.Handler;
import com.medusa.gruul.goods.api.model.dto.CopyGoodsDTO;
import com.medusa.gruul.goods.api.model.dto.CopyProductDTO;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */
public abstract class AbstractCopyHandler implements Handler<CopyProductDTO> {
    @Override
    public CopyProductDTO handle(Object... params) {
        return this.handler(this.cast(params[0], CopyGoodsDTO.class));
    }

    /**
     * 一键复制
     *
     * @param copyGoodsDto copyGoodsDto
     * @return CopyProductDTO
     */
    public abstract CopyProductDTO handler(CopyGoodsDTO copyGoodsDto);
}
