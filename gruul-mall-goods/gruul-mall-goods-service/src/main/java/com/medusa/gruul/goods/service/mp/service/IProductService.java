package com.medusa.gruul.goods.service.mp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.custom.aggregation.classify.dto.CategoryRankDTO;
import com.medusa.gruul.common.model.base.ShopProductKey;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.model.dto.*;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import com.medusa.gruul.goods.api.model.param.*;
import com.medusa.gruul.goods.api.model.vo.*;
import com.medusa.gruul.goods.service.model.dto.ConsignmentProductDTO;
import com.medusa.gruul.goods.service.model.dto.ShopProductSkuIdDTO;
import com.medusa.gruul.goods.service.model.param.PurchaseProductParam;
import com.medusa.gruul.goods.service.model.vo.ProductDeliveryVO;
import com.medusa.gruul.goods.service.model.vo.ProductNumVo;
import com.medusa.gruul.goods.service.model.vo.SupplierIssueProductListVO;
import com.medusa.gruul.order.api.model.OrderCompletedDTO;
import com.medusa.gruul.shop.api.model.dto.ShopsEnableDisableDTO;
import com.medusa.gruul.storage.api.vo.ProductSaleVolumeVO;
import com.medusa.gruul.storage.api.vo.ProductSkusVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;

/**
 * 商品信息表 服务类
 *
 * <AUTHOR>
 * @since 2022-03-04
 */
public interface IProductService extends IService<Product> {
   /**
    * 生成二维码
    * @param qrCodeDTO
    * @param response
    */
 void generateQRCoded(QRCodeDTO qrCodeDTO, HttpServletResponse response);
    /**
     * 商品发布
     *
     * @param productDto 商品信息Dto
     */
    void issueProduct(ProductDTO productDto);

    /**
     * 商品删除
     *
     * @param productIds 商品ids
     */
    void deleteProductList(Set<Long> productIds);

    /**
     * 产品上下架
     *
     * @param productStatusChange 商品状态更改信息
     * @param status              产品上下架状态
     */
    void updateProductStatus(ProductStatusChangeDTO productStatusChange, ProductStatus status);

    /**
     * 商品信息修改
     *
     * @param productDto 商品信息
     */
    void updateProduct(ProductDTO productDto);


    /**
     * 查询单个商品信息
     *
     * @param id     商品id
     * @param shopId 店铺id
     * @param score  是否是有效的热度得分
     * @return 商品详情
     */
    ProductVO getProductById(Long id, Long shopId, boolean score);

    /**
     * 查询商品信息列表
     *
     * @param productParam 查询条件
     * @return 符合条件的商品信息
     */
    IPage<ProductVO> getProductList(ProductParam productParam);

    IPage<ProductVO> getProductInfoByNo(ProductParam productParam);
    /**
     * 平台获取商品信息
     *
     * @param platformProductParam 查询条件
     * @return 符合条件得所有商品信息
     */
    Page<PlatformProductVO> queryProductInfoByParam(PlatformProductParam platformProductParam);


    //==============================C端========================================================
    //==============================C端=========================================================


    /**
     * 根据平台类目Id 获取商品信息List
     *
     * @param platformCategoryParam 商品查询param by平台类目
     * @param levelCategoryList     三级类目
     * @return Page<ApiPlatformProductVO>
     */
    Page<ApiPlatformProductVO> getProductInfoByPlatformCategoryId(List<Long> levelCategoryList, PlatformCategoryParam platformCategoryParam);

    /**
     * 根据排序type获取店铺商品信息
     *
     * @param apiProductParam apiProductParam
     * @return Page<ApiProductVo>
     */
    Page<ApiProductVO> getProductInfoByParam(ApiProductParam apiProductParam);

    /**
     * 根据平台类目三级Id 获取商铺信息List
     *
     * @param platformCategoryParam platformCategoryParam
     * @return Page<ApiPlatformProductVO>
     */
    Page<ApiPlatformProductVO> getProductInfoByPlatformThirdlyCategoryId(PlatformCategoryParam platformCategoryParam);


    /**
     * 店铺状态改变 启用/禁用
     *
     * @param shopsEnableDisable 店铺禁用启用参数
     */
    void shopChange(ShopsEnableDisableDTO shopsEnableDisable);

    /**
     * 获取商品信息
     *
     * @param shopId    店铺id
     * @param productId 商品id
     * @return 商品信息
     */
    Product getProductInfo(Long shopId, Long productId);

    /**
     * 批量获取商品 信息
     *
     * @param shopProductKeys shopId,productId
     * @return map<{ shopId, productId }, product>
     */
    Map<ShopProductKey, Product> getProductBatch(Set<ShopProductKey> shopProductKeys);

    /**
     * 获取店铺商品list
     *
     * @param shopId
     * @return List<Product>
     */
    List<Product> getProductListByShopId(Long shopId);

    /**
     * 获取商品信息
     *
     * @param productSupplier Supplier
     * @param key             redisKey
     * @return Product
     */
    Product init(Supplier<Product> productSupplier, String key);

    /**
     * 获取店铺在售商品数量
     *
     * @param shopId 店铺id
     * @return 店铺在售商品数量
     */
    Long getShopSalesProductCount(Long shopId);

    /**
     * 获取平台三级类目下商品数量
     *
     * @param thirdIds 平台类目三级ids
     * @return map<平台类目ids, 商品数量>
     */
    List<ProductNumVo> getProductNumByPlatformThirdCategoryId(Set<Long> thirdIds);

    /**
     * 获取随机商品
     *
     * @param productRandomParam 参数
     * @return 商品
     */
    Page<Product> randomGoods(ProductRandomParam productRandomParam);

    /**
     * 获取商品状态数量
     *
     * @return List<ProductStatusQuantityVO>
     */
    Map<ProductStatus, Long> getGoodsQuantity();

    /**
     * 获取今日新增商品数量
     *
     * @return 今日新增商品数量
     */
    Long getTodayAddGoodsQuantity();


    /**
     * 根据类目id 及类目级别 获取商品信息
     *
     * @param categoryRank 类目级别dto
     * @return Page<ApiProductVO>
     */
    Page<ApiProductVO> getProductInfoByCategoryId(CategoryRankDTO categoryRank);

    /**
     * pc端-看了又看
     *
     * @param productRandomParam 参数
     * @return 看了又看
     */
    Page<ApiProductLookAndSeeVO> lookAndSeePage(ProductRandomParam productRandomParam);

    /**
     * 根据平台三级类目ids 获取  ApiProductVO
     *
     * @param categoryRank 类目等级dto
     * @return ApiProductVO
     */
    Page<ApiProductVO> getApiProductInfoByPlatformCategoryId(CategoryRankDTO categoryRank);

    /**
     * pc端-店铺热销
     *
     * @param shopId 店铺id
     * @param size   查询数量
     * @return 店铺热销
     */
    List<ProductSaleVolumeVO> shopHotSales(Long shopId, Long size);

    /**
     * pc端-热门关注
     *
     * @return 热门关注
     */
    List<ApiProductPopularAttentionVO> shopPopularAttention();

    /**
     * 分页获取商品和规格信息
     *
     * @param productParam 商品查询参数
     * @return 商品规格信息
     */
    IPage<ProductSkusVO> getProductSkus(ProductParam productParam);

    /**
     * 保存供应商商品评分
     *
     * @param orderCompleted 订单完成数据
     */
    void saveSupplierProductRate(OrderCompletedDTO orderCompleted);

    /**
     * 获取条件商品信息 包含以删除商品信息
     *
     * @param shopId    店铺id
     * @param productId 商品id
     * @return 商品信息
     */
    Product getConditionProductInfo(Long shopId, Long productId);

    /**
     * 查询运费相关信息
     *
     * @param productSkuIds
     * @return
     */
    List<ProductDeliveryVO> getProductDelivery(List<ShopProductSkuIdDTO> productSkuIds);

    /**
     * 查看签约类目下是否存在商品
     *
     * @param signingCategorySecondIds 二级签约类目ids
     * @param shopId                   店铺id
     * @return 是否存在
     */
    boolean getSigningCategoryProduct(Set<Long> signingCategorySecondIds, Long shopId);

    /**
     * 商品名称修改
     *
     * @param id   商品id
     * @param name 商品名称
     */
    void updateProductName(Long id, String name);

    /**
     * 获取商品库存基础信息
     *
     * @param param 查询param
     * @return ProductStockVO
     */
    IPage<ProductStockVO> getProductStockBaseInfo(ProductStockParam param);

    /**
     * 获取采购发布商品
     *
     * @param param 查询param
     * @return IPage<SupplierIssueProductListVO>
     */
    IPage<SupplierIssueProductListVO> getPurchaseIssueProducts(PurchaseProductParam param);

    /**
     * 已采购发布商品装修修改
     *
     * @param id 商品id
     */
    void purchaseIssueProductUpdateStatus(Long id);

    /**
     * 供应商商品状态更新
     *
     * @param supplierGoodsUpdateStatus 更新的商品数据
     */
    void supplierGoodsUpdateStatus(SupplierGoodsUpdateStatusDTO supplierGoodsUpdateStatus);

    /**
     * 已铺货代销商品
     *
     * @param purchaseProductParam 查询参数
     * @return    IPage<已铺货的代销商品>
     */
    IPage<SupplierIssueProductListVO> getPaveGoods(PurchaseProductParam purchaseProductParam);

    /**
     * 已铺货代销商品上架
     *
     * @param productId 商品id
     */
    void consignmentProductUpdateStatus(Long productId);

    /**
     * 供应商商品删除
     *
     * @param keys 供应商 id 、商品 id 集合
     */
    void supplierForceGoodsStatus(Set<ShopProductKey> keys);

    /**
     * 供应商商品信息修改
     *
     * @param supplierProduct 商品信息
     */
    void supplierUpdateGoods(Product supplierProduct);

    /**
     * 代销商品信息
     *
     * @param id 商品id
     * @return 商品信息
     */
    ProductVO getConsignmentProductInfo(Long id);

    /**
     * 代销商品修改
     *
     * @param consignmentProduct 代销商品修改DTO
     */
    void consignmentProductUpdate(ConsignmentProductDTO consignmentProduct);


    /**
     * 根据{@code supplierId}和{@code productId}获取商品信息
     *
     * @param supplierId 供应商ID
     * @param productId  商品id
     * @return #{@link Product}
     */
    Product getProductBySupplierIdAndProductId(Long supplierId, Long productId);

    /**
     * 根据shopId查询商品列表首页专用
     * @param productRenderParam
     * @return
     */

    IPage<ProductRenderVO> queryProductRenderVO(ProductRenderParam productRenderParam);


    /**
     * 医护用户获取多产品信息
     * @param yihuProductQueryDTO
     * @return
     */
    List<ProductVO> getProductByName(YihuProductQueryDTO yihuProductQueryDTO);
}
