package com.medusa.gruul.goods.service.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * date 2022/4/22
 */
@Getter
@Setter
@ToString
public class CategorySortDTO {

    @NotNull
    private Long parentId;

    @NotNull
    @Size(min = 1)
    private List<Long> sortedIds;

}
