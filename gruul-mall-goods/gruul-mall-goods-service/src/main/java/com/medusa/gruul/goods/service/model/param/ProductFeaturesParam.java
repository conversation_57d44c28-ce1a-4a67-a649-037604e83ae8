package com.medusa.gruul.goods.service.model.param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.goods.api.model.enums.FeaturesType;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 产品特性 param
 *
 * <AUTHOR>
 * @Description ProductFeaturesParam.java
 * @date 2023-06-15 18:38
 */
@Data

public class ProductFeaturesParam extends Page<Object> {
    /**
     * 名称
     */
    private String name;

    /**
     * 产品特性type
     */
    @NotNull
    private FeaturesType featuresType;
}
