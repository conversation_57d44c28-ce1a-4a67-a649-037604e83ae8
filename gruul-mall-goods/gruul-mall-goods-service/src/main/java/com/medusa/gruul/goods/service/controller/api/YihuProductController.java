package com.medusa.gruul.goods.service.controller.api;

import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.goods.api.model.dto.YihuProductQueryDTO;
import com.medusa.gruul.goods.service.model.dto.YihuProductDTO;
import com.medusa.gruul.goods.service.service.YihuProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/yihu/product")
public class YihuProductController {
    private final YihuProductService yihuProductService;
    @PostMapping
    @Log("医护获取商品信息")
    public Result<List<YihuProductDTO>> getProductByName(@RequestBody @Valid YihuProductQueryDTO yihuProductQueryDTO){
      return Result.ok(yihuProductService.getProductsByName(yihuProductQueryDTO));
    }
}
