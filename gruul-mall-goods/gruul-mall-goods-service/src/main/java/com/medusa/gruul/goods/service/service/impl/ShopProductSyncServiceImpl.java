package com.medusa.gruul.goods.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.medusa.gruul.common.model.base.ShopProductKey;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.mp.config.MybatisPlusConfig;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.entity.ProductCategory;
import com.medusa.gruul.goods.api.entity.ProductFeatures;
import com.medusa.gruul.goods.api.model.dto.ProductDTO;
import com.medusa.gruul.goods.api.model.dto.ProductDeleteDTO;
import com.medusa.gruul.goods.api.model.dto.ProductStatusChangeDTO;
import com.medusa.gruul.shop.api.model.dto.ShopProductSyncDTO;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import com.medusa.gruul.goods.service.mp.service.IProductCategoryService;
import com.medusa.gruul.goods.service.mp.service.IProductFeatureService;
import com.medusa.gruul.goods.service.mp.service.IProductService;
import com.medusa.gruul.goods.service.service.ShopProductSyncService;
import com.medusa.gruul.shop.api.entity.Shop;
import com.medusa.gruul.shop.api.entity.ShopDataSyncRecord;
import com.medusa.gruul.shop.api.enums.*;
import com.medusa.gruul.shop.api.model.dto.ShopQueryDTO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import com.medusa.gruul.storage.api.dto.StorageSpecSkuDTO;
import com.medusa.gruul.storage.api.rpc.StorageRpcService;
import io.vavr.Tuple;
import io.vavr.Tuple3;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @describe 店铺产品同步
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopProductSyncServiceImpl implements ShopProductSyncService {

    @Value("${gruul.shop.defaultShopId}")
    private Long defaultShopId;

    private final IProductService productService;

    private final StorageRpcService storageRpcService;
    private final IProductCategoryService categoryService;
    private final IProductFeatureService productFeatureService;

    private ShopRpcService shopRpcService;


    @Override
    public void shopProduct(ShopProductSyncDTO shopProduct) {
        //检查当前操作的店铺是否为默认店铺
        if (!ISystem.shopIdOpt().get().equals(defaultShopId)) {
            throw new ServiceException("操作权限不足！");
        }
        log.debug("商品同步数据条件:{}", JSONUtil.toJsonStr(shopProduct));
        //需要同步的店铺
        ShopQueryDTO shopQueryDTO = new ShopQueryDTO();
        shopQueryDTO.setStatus(ShopStatus.NORMAL).setShopType(ShopType.PREFERRED);
        List<Shop> syncShopAll = shopRpcService.getShop(shopQueryDTO);
        //只同步指定类型
        if (shopProduct.getTargetShopId() != null) {
            syncShopAll = syncShopAll.stream()
                    .filter(shop -> shop.getId().equals(shopProduct.getTargetShopId()))
                    .filter(shop -> !shop.getId().equals(defaultShopId))
                    .collect(Collectors.toList());
        } else {
            syncShopAll = syncShopAll.stream()
                    .filter(shop -> !shop.getId().equals(defaultShopId))
                    .collect(Collectors.toList());
        }
        //是否有店铺需要同步
        if (ObjectUtils.isEmpty(syncShopAll)) {
            log.debug("没有需要同步的店铺");
            return;
        }
        //同步特性
        syncFeature(syncShopAll);
        //同步店铺类目
        syncCategory(syncShopAll);
        //同步商品
        List<Product> productList;
        if (shopProduct.getProductId() != null) {
            productList = Lists.newArrayList();
            Product product = productService.getProductInfo(defaultShopId, shopProduct.getProductId());
            productList.add(product);
        } else {
            productList = productService.getProductListByShopId(defaultShopId);
        }
        //处理需要同步的数据
        List<ProductDTO> productSyncList = Lists.newArrayList();
        productList.forEach(product -> ISystem.shopId(product.getId(), () -> {
            Set<ShopProductKey> shopProductKeys = Sets.newHashSet();
            ShopProductKey shopProductKey = new ShopProductKey();
            shopProductKey.setProductId(product.getId())
                    .setShopId(product.getShopId());
            shopProductKeys.add(shopProductKey);
            List<StorageSpecSkuDTO> storageSpecSku = storageRpcService.getStorageSpecSku(shopProductKeys);
            ProductDTO productDTO = coverShopProductDTO(product, storageSpecSku.get(0));
            productSyncList.add(productDTO);
        }));
        for (Shop shop : syncShopAll) {
            if (shop.getId().equals(defaultShopId)) {
                log.debug("默认店铺不同步商品");
                continue;
            }
            ISystem.shopId(shop.getId(), () -> {
                //查询目标店铺商品
                List<Product> targetShopProductList = productService.getProductListByShopId(shop.getId());
                //原同步数据
                List<ShopDataSyncRecord> recordTargetShopId = shopRpcService.getLatestRecordByTargetShopId(shop.getId(),
                        ShopDataSyncType.PRODUCT);
                //处理目标店铺商品数据
                List<ProductDTO> targetShopProductSyncList = Lists.newArrayList();
                targetShopProductList.forEach(product -> ISystem.shopId(product.getId(), () -> {
                    Set<ShopProductKey> shopProductKeys = Sets.newHashSet();
                    ShopProductKey shopProductKey = new ShopProductKey();
                    shopProductKey.setProductId(product.getId())
                            .setShopId(product.getShopId());
                    shopProductKeys.add(shopProductKey);
                    List<StorageSpecSkuDTO> storageSpecSku = storageRpcService.getStorageSpecSku(shopProductKeys);
                    ProductDTO productDTO = coverShopProductDTO(product, storageSpecSku.get(0));
                    targetShopProductSyncList.add(productDTO);
                }));
                //处理删除的
                syncDeleteProductV2(recordTargetShopId, defaultShopId, productSyncList, shop.getId(), targetShopProductSyncList);
                //处理需要同步的数据
                Tuple3<List<ProductDTO>, List<ProductDTO>, List<ShopDataSyncRecord>> tuple3 = processProductChangesV2(recordTargetShopId, defaultShopId, productSyncList, shop.getId(), targetShopProductSyncList);
                //新增
                for (ProductDTO productDTO : tuple3._1()) {
                    try {
                        log.debug("新增商品同步:{}", JSONUtil.toJsonStr(productDTO));
                        productService.issueProduct(productDTO);
                    } catch (Exception e) {
                        log.error("商品同步失败, sycnShopId:{}, syncInfo:{}", shop.getId(), JSONUtil.toJsonStr(productDTO), e);
                        Optional<ShopDataSyncRecord> optionalShopDataSyncRecord = tuple3._3().stream()
                                .filter(p -> p.getTargetDataId().equals(productDTO.getId()))
                                .findFirst();
                        if (optionalShopDataSyncRecord.isPresent()) {
                            ShopDataSyncRecord shopDataSyncRecord = optionalShopDataSyncRecord.get();
                            shopDataSyncRecord.setStatus(ShopDataStatus.FAIL)
                                    .setSyncMessage(JSON.parseObject(JSONUtil.toJsonStr(e)));
                        }

                    }
                }
                //修改
                for (ProductDTO productDTO : tuple3._2()) {
                    try {
                        log.debug("修改商品同步:{}", JSONUtil.toJsonStr(productDTO));
                        productService.updateProduct(productDTO);
                    } catch (Exception e) {
                        log.error("修改商品同步失败, sycnShopId:{}, syncInfo:{}", shop.getId(), JSONUtil.toJsonStr(productDTO), e);
                        Optional<ShopDataSyncRecord> optionalShopDataSyncRecord = tuple3._3().stream()
                                .filter(p -> p.getTargetDataId().equals(productDTO.getId()))
                                .findFirst();
                        if (optionalShopDataSyncRecord.isPresent()) {
                            ShopDataSyncRecord shopDataSyncRecord = optionalShopDataSyncRecord.get();
                            shopDataSyncRecord.setStatus(ShopDataStatus.FAIL);
                        }
                    }
                }
                shopRpcService.createShopSyncRecord(tuple3._3());
            });
        }
    }

    //同步记录
    private void convertSyncRecordV2(ShopDataSyncChangeType type, ShopDataSyncRecord shopDataSyncRecord, Long shopId,
                                     ProductDTO productDTO, Long targetShopId, ProductDTO targetProductDTO) {
        if (Objects.equals(type.getValue(), ShopDataSyncChangeType.ADD.getValue())) {
            shopDataSyncRecord.setTargetDataId(productDTO.getId())
                    .setTargetDataNo(productDTO.getNo());
        } else {
            shopDataSyncRecord.setTargetDataId(targetProductDTO.getId())
                    .setTargetDataNo(targetProductDTO.getNo());
        }
        if (shopDataSyncRecord.getStatus() == null) {
            shopDataSyncRecord.setStatus(ShopDataStatus.SUCCESS);
        }
        if (shopDataSyncRecord.getSyncDataId() == null) {
            shopDataSyncRecord.setSyncDataId(productDTO.getId());
        }
        if (shopDataSyncRecord.getSyncDataNo() == null) {
            shopDataSyncRecord.setSyncDataNo(productDTO.getNo());
        }
        shopDataSyncRecord.setShopId(shopId)
                .setSyncType(ShopDataSyncType.PRODUCT)
                .setTargetShopId(targetShopId)
                .setSyncChangeType(type)
                .setSyncChangeData(JSON.parseObject(JSONUtil.toJsonStr(productDTO)))
        ;
    }

    //同步产品特性
    private void syncFeature(List<Shop> syncShop) {
        List<ProductFeatures> productFeaturesList = ISystem.shopId(defaultShopId, () -> productFeatureService.list());
        for (Shop shop : syncShop) {
            ISystem.shopId(shop.getId(), () -> {
                //处理值
                productFeaturesList.forEach(productFeatures -> productFeatures.setShopId(shop.getId()));
                productFeatureService.saveOrUpdateBatch(productFeaturesList);
            });
        }
    }

    //同步店铺类目
    private void syncCategory(List<Shop> syncShop) {
        List<ProductCategory> categoryList = ISystem.shopId(defaultShopId, () -> categoryService.list());
        for (Shop shop : syncShop) {
            ISystem.shopId(shop.getId(), () -> {
                //处理值
                categoryService.saveOrUpdateBatch(categoryList);
            });
        }
    }

    //同步删除的产品, 返回未删除的数据
    private List<Product> syncDeleteProduct(List<Shop> syncShop, List<Product> productList) {
        List<Product> delProductList = productList.stream().filter(product -> product.getDeleted()).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(delProductList)) {
            return productList;
        }
        //先下架
        Set<Long> offProductIds = delProductList.stream().filter(product -> ProductStatus.SELL_OFF.equals(product.getStatus())).map(Product::getId).collect(Collectors.toSet());
        if (!ObjectUtils.isEmpty(offProductIds)) {
            for (Shop shop : syncShop) {
                ISystem.shopId(shop.getId(), () -> {
                    ProductStatusChangeDTO productStatusChangeDTO = new ProductStatusChangeDTO();
                    Set<ShopProductKey> keys = new HashSet<>();
                    //组装keys
                    offProductIds.forEach(id -> {
                        ShopProductKey shopProductKey = new ShopProductKey();
                        shopProductKey.setProductId(id);
                        shopProductKey.setShopId(ISecurity.shopIdOrISysMust());
                        keys.add(shopProductKey);
                    });
                    productStatusChangeDTO.setKeys(keys);
                    log.debug("同步下架商品:{}", JSONUtil.toJsonStr(productStatusChangeDTO));
                    productService.updateProductStatus(productStatusChangeDTO, ProductStatus.SELL_OFF);
                });
            }
        }
        //处理删除
        Set<Long> delProductIds = delProductList.stream().map(Product::getId).collect(Collectors.toSet());
        for (Shop shop : syncShop) {
            ISystem.shopId(shop.getId(), () -> {
                try {
                    productService.deleteProductList(delProductIds);
                } catch (Exception ignored) {
                }

            });
        }
        return productList.stream().filter(product -> !product.getDeleted()).collect(Collectors.toList());
    }

    /**
     * 同步删除产品
     *
     * @param recordTargetShopId    之前同步数据
     * @param shopId                当前店铺ID
     * @param productList           当前店铺产品列表
     * @param targetShopId          目标店铺ID
     * @param targetShopProductList 目标店铺产品列表
     */
    private void syncDeleteProductV2(List<ShopDataSyncRecord> recordTargetShopId, Long shopId, List<ProductDTO> productList,
                                     Long targetShopId, List<ProductDTO> targetShopProductList) {
        Map<Long, ShopDataSyncRecord> oldRecordMap = recordTargetShopId.stream().collect(Collectors.toMap(ShopDataSyncRecord::getSyncDataId, v -> v));
        List<ShopDataSyncRecord> shopDataSyncRecordList = Lists.newArrayList();
        //要删除丶数据
        List<ProductDTO> delProductList = productList.stream().filter(product -> product.getDeleted()).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(delProductList)) {
            return;
        }
        //目标店铺产品
        Map<Long, ProductDTO> delTargetProductMap = targetShopProductList.stream().collect(Collectors.toMap(ProductDTO::getId, v -> v));

        //处理删除，判断目标店铺是否已删除
        Set<ProductDTO> delProducts = Sets.newHashSet();
        delProductList.forEach(p -> {
            //判断数据是否产生变化
            ShopDataSyncRecord shopDataSyncRecord = oldRecordMap.get(p.getId());
            if (null != shopDataSyncRecord && null != shopDataSyncRecord.getSyncData() && null != shopDataSyncRecord.getTargetDataId()) {
                //如果目标店铺不存在，则不处理
                if (!delTargetProductMap.containsKey(shopDataSyncRecord.getTargetDataId())) {
                    return;
                }
                ProductDTO productDTO = delTargetProductMap.get(shopDataSyncRecord.getTargetDataId());
                if (productDTO.getDeleted()) {
                    //已删除的不处理
                    return;
                }
                JSONObject syncData = shopDataSyncRecord.getSyncData();
                if (!equalsJson(syncData, JSONObject.parseObject(JSONUtil.toJsonStr(p)))) {
                    //记录
                    ProductDTO targetProduct = JSONUtil.toBean(syncData.toJSONString(), ProductDTO.class);
                    ShopDataSyncRecord delRecord = new ShopDataSyncRecord();
                    delRecord.setSyncDataId(p.getId()).setSyncData(JSON.parseObject(JSONUtil.toJsonStr(p)));
                    convertSyncRecordV2(ShopDataSyncChangeType.DELETE, delRecord, shopId, p,
                            targetShopId, targetProduct);
                    shopDataSyncRecordList.add(delRecord);
                    //del
                    delProducts.add(productDTO);
                }
            }
        });
        //先下架
        List<ProductDTO> offProductIds = delProducts.stream().filter(product -> ProductStatus.SELL_ON.equals(product.getStatus())).collect(Collectors.toList());

        if (!ObjectUtils.isEmpty(offProductIds)) {
            ProductStatusChangeDTO productStatusChangeDTO = new ProductStatusChangeDTO();
            Set<ShopProductKey> keys = new HashSet<>();
            //组装keys
            offProductIds.forEach(product -> {
                ShopProductKey shopProductKey = new ShopProductKey();
                shopProductKey.setProductId(product.getId());
                shopProductKey.setShopId(targetShopId);
                keys.add(shopProductKey);
            });
            if (!keys.isEmpty()) {
                productStatusChangeDTO.setKeys(keys);
                log.debug("同步下架商品:{}", JSONUtil.toJsonStr(productStatusChangeDTO));
                productService.updateProductStatus(productStatusChangeDTO, ProductStatus.SELL_OFF);
            }
        }
        //处理删除
        Set<Long> delProductIds = offProductIds.stream().map(ProductDTO::getId)
                .collect(Collectors.toSet());
        try {
            if (!ObjectUtils.isEmpty(delProductIds)) {
                log.debug("同步删除商品:{}", JSONUtil.toJsonStr(delProductIds));
                productService.deleteProductList(delProductIds);
                shopRpcService.createShopSyncRecord(shopDataSyncRecordList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //判断是否相同,相同true
    private boolean equalsJson(JSONObject a, JSONObject b) {
        cn.hutool.json.JSON obj1 = JSONUtil.parse(a.toJSONString());
        cn.hutool.json.JSON obj2 = JSONUtil.parse(b.toJSONString());
        return obj1.equals(obj2);
    }

    /**
     * 处理是新增还是修改，如果是新增，则去除原id
     *
     * @param productDTOList        需要同步的产品
     * @param targetShopProductList 目标店铺产品
     * @return
     */
    private List<ProductDTO> processProductChanges(List<ProductDTO> productDTOList, List<ProductDTO> targetShopProductList) {
        //targetShopProductList转id map<no, Product>
        Map<String, ProductDTO> targetShopProductIds = targetShopProductList.stream().collect(Collectors.toMap(ProductDTO::getNo, product -> product));
        List<ProductDTO> retProductList = Lists.newArrayList();
        for (ProductDTO productDTO : productDTOList) {
            //如果存在，则是更新
            if (targetShopProductIds.containsKey(productDTO.getNo())) {
                ProductDTO targetProduct = targetShopProductIds.get(productDTO.getNo());
                //处理id
                productDTO.setId(targetProduct.getId());
                retProductList.add(productDTO);
            } else {
                //新增
                productDTO.setId(null);
                retProductList.add(productDTO);
            }
        }
        return retProductList;
    }

    /**
     * 处理是新增还是修改，如果是新增，则去除原id
     *
     * @param shopId                需要同步的产品店铺id
     * @param productDTOList        需要同步的产品
     * @param targetShopId          需要同步的目标店铺id
     * @param targetShopProductList 目标店铺产品
     * @return List<ProductDTO> add, List<ProductDTO> update, List<ShopDataSyncRecord> 记录
     */
    private Tuple3<List<ProductDTO>, List<ProductDTO>, List<ShopDataSyncRecord>> processProductChangesV2(List<ShopDataSyncRecord> recordTargetShopId, Long shopId, List<ProductDTO> productDTOList,
                                                                                                         Long targetShopId, List<ProductDTO> targetShopProductList) {

        //原同步记录, syncDataId -> v
        Map<Long, ShopDataSyncRecord> oldRecordMap = recordTargetShopId.stream().collect(Collectors.toMap(ShopDataSyncRecord::getSyncDataId, v -> v));

        //同步记录
        List<ShopDataSyncRecord> recordList = Lists.newArrayList();
        //targetShopProductList转id map<no, Product>
        Map<String, ProductDTO> targetShopProductIds = targetShopProductList.stream().collect(Collectors.toMap(ProductDTO::getNo, product -> product));
        List<ProductDTO> updateProductList = Lists.newArrayList();
        List<ProductDTO> addProductList = Lists.newArrayList();
        for (ProductDTO productDTO : productDTOList) {
            if (productDTO.getDeleted()) {
                //不处理删除
                continue;
            }

            JSONObject productJson = JSON.parseObject(JSONUtil.toJsonStr(productDTO));
            //判断数据是否变更
            ShopDataSyncRecord oldRecord = oldRecordMap.get(productDTO.getId());
            if (null != oldRecord && null != oldRecord.getSyncData() && equalsJson(productJson, oldRecord.getSyncData())) {
                continue;
            }
            ProductDTO syncProductDTO = new ProductDTO();
            BeanUtil.copyProperties(productDTO, syncProductDTO);
            syncProductDTO.setProductParameters(productDTO.getProductParameters())
                    .setProductAttributes(productDTO.getProductAttributes())
                    .setSkus(productDTO.getSkus())
                    .setSpecGroups(productDTO.getSpecGroups())
                    .setDistributionMode(productDTO.getDistributionMode())
                    .setServiceIds(productDTO.getServiceIds());

            ShopDataSyncRecord shopDataSyncRecord = new ShopDataSyncRecord();
            shopDataSyncRecord.setSyncDataId(productDTO.getId()).setSyncDataNo(productDTO.getNo()).setSyncData(JSON.parseObject(JSONUtil.toJsonStr(productDTO)));

            //如果存在，则是更新
            ProductDTO targetProduct = targetShopProductIds.get(productDTO.getNo());
            if (null != targetProduct) {
                //处理id
                syncProductDTO.setId(targetProduct.getId());
                updateProductList.add(syncProductDTO);
                //记录
                convertSyncRecordV2(ShopDataSyncChangeType.UPDATE, shopDataSyncRecord, shopId, syncProductDTO, targetShopId, targetProduct);
            } else {
                syncProductDTO.setId(MybatisPlusConfig.IDENTIFIER_GENERATOR.nextId(new Product()).longValue());
                //新增
                addProductList.add(syncProductDTO);
                convertSyncRecordV2(ShopDataSyncChangeType.ADD, shopDataSyncRecord, shopId, syncProductDTO, targetShopId, targetProduct);
            }

            recordList.add(shopDataSyncRecord);
        }
        return Tuple.of(addProductList, updateProductList, recordList);
    }


    @Override
    public void productDelete(ProductDeleteDTO productDelete) {

    }

    //转换
    public ProductDTO coverShopProductDTO(Product product, StorageSpecSkuDTO storageSpecSku) {
        ProductDTO productDTO = new ProductDTO();
        BeanUtil.copyProperties(product, productDTO);
        productDTO.setShopCategory(ObjectUtil.isEmpty(product.getExtra()) ? null : product.getExtra().getShopCategory())
                .setProductAttributes(ObjectUtil.isEmpty(product.getExtra()) ? null : product.getExtra().getProductAttributes())
                .setProductType(product.getProductType())
                .setConsignmentPriceSetting(ObjectUtil.isEmpty(product.getExtra()) ? null : product.getExtra().getConsignmentPriceSetting())
                .setDistributionMode(product.getDistributionMode())
                .setPlatformCategory(ObjectUtil.isEmpty(product.getExtra()) ? null : product.getExtra().getPlatformCategory())
                .setProductParameters(ObjectUtil.isEmpty(product.getExtra()) ? null : product.getExtra().getProductParameters())
                .setSellType(product.getSellType())
                .setServiceIds(product.getServiceIds())
                .setShopCategory(ObjectUtil.isEmpty(product.getExtra()) ? null : product.getExtra().getShopCategory())
        ;
        if (!ObjectUtil.isEmpty(storageSpecSku)) {
            productDTO.setSkus(storageSpecSku.getSkus());
            productDTO.setSpecGroups(storageSpecSku.getSpecGroups());
        }
        productDTO.setDeleted(product.getDeleted());
        return productDTO;
    }

    @Autowired
    @Lazy
    public void setShopRpcService(ShopRpcService shopRpcService) {
        this.shopRpcService = shopRpcService;
    }
}
