package com.medusa.gruul.goods.service.functions.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.goods.api.constant.GoodsConstant;
import com.medusa.gruul.goods.api.enums.CopyGoodsType;
import com.medusa.gruul.goods.api.json.tb.TbItemJsonDTO;
import com.medusa.gruul.goods.api.model.dto.CopyGoodsDTO;
import com.medusa.gruul.goods.api.model.dto.CopyProductDTO;
import com.medusa.gruul.goods.api.model.enums.GoodsError;
import com.medusa.gruul.goods.service.functions.annotation.CopyAnnotation;
import com.medusa.gruul.storage.api.dto.SpecDTO;
import com.medusa.gruul.storage.api.dto.SpecGroupDTO;
import com.medusa.gruul.storage.api.enums.LimitType;
import com.medusa.gruul.storage.api.enums.StockType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/11
 * @describe 淘宝商品处理
 */
@Slf4j
@Component
@CopyAnnotation(CopyGoodsType.TaoBao)
public class TaoBaoHandler extends AbstractCopyHandler {
    /**
     * 淘宝一键复制
     *
     * @param copyGoodsDto copyGoodsDto
     * @return CopyProductDTO
     */
    @Override
    public CopyProductDTO handler(CopyGoodsDTO copyGoodsDto) {
        String goodsUrl = copyGoodsDto.getGoodsUrl();
        String apikey = copyGoodsDto.getApikey();
        String copyGoodsUrl = GoodsConstant.COPY_GOODS_URL;
        String itemId = handlerTbUrl(goodsUrl);
        if (StrUtil.isEmpty(itemId)) {
            log.error("商品链接地址:{}", goodsUrl);
            throw GoodsError.LINK_ERROR.exception();
        }
        String body = HttpUtil.createGet(StrUtil.format(copyGoodsUrl, CopyGoodsType.TaoBao.getType(), apikey, itemId)).timeout(1000 * 60).execute().body();
        JSONObject entries = JSONUtil.parseObj(body);
        CopyProductDTO productDTO = new CopyProductDTO();
        //正常返回0000
        if (!GoodsConstant.SUCCESS_CODE.equals(entries.get(GoodsConstant.RET_CODE).toString())) {
            throw GoodsError.API_EXCEPTION_99API.exception(entries.get(GoodsConstant.DATA).toString(), Integer.parseInt(entries.get(GoodsConstant.RET_CODE).toString()));
        }
        JSONObject item = entries.getJSONObject(GoodsConstant.DATA).getJSONObject(GoodsConstant.ITEM);
        TbItemJsonDTO tbItemJsonDto = item.toBean(TbItemJsonDTO.class);
        if (StrUtil.isNotEmpty(tbItemJsonDto.getTitle())) {
            productDTO.setName(StrUtil.sub(tbItemJsonDto.getTitle(), 0, 50));
        }
        List<String> images = tbItemJsonDto.getImages().stream().limit(CommonPool.NUMBER_SIX).toList();
        images = images.stream().map(this::handlerPic).collect(Collectors.toList());
        productDTO.setAlbumPics(StrUtil.join(StrUtil.COMMA, images));
        productDTO.setPic(images.get(CommonPool.NUMBER_ZERO));
        productDTO.setDetail(tbItemJsonDto.getDesc());
        String videoUrl = null;
        if (CollUtil.isNotEmpty(tbItemJsonDto.getVideos())) {
            String json = tbItemJsonDto.getVideos().get(CommonPool.NUMBER_ZERO);
            videoUrl = (String) JSONUtil.parseObj(json).get(GoodsConstant.URL);
        }
        productDTO.setVideoUrl(videoUrl);
        handlerTbSku(productDTO, tbItemJsonDto);
        return productDTO;
    }


    /**
     * 处理淘宝商品Url
     *
     * @param goodsUrl 淘宝URL
     * @return 商品id
     */
    private String handlerTbUrl(String goodsUrl) {
        Map<String, List<String>> stringListMap = HttpUtil.decodeParams(goodsUrl, (String) null);
        List<String> ids = stringListMap.get(GoodsConstant.ID);
        return CollUtil.isEmpty(ids) ? null : ids.get(0);
    }

    /**
     * 处理淘宝sku和规格
     *
     * @param productDTO    封装结果
     * @param tbItemJsonDto json
     */
    private void handlerTbSku(CopyProductDTO productDTO, TbItemJsonDTO tbItemJsonDto) {
        List<CopyProductDTO.CopySkuDto> skuDTOList = tbItemJsonDto.getSku().stream()
                .skip(CommonPool.NUMBER_ONE)
                .map(tbSkuJsonDto -> {
                    CopyProductDTO.CopySkuDto skuDTO = new CopyProductDTO.CopySkuDto();
                    //规格值
                    List<String> specs = new ArrayList<>();
                    String skuName = tbSkuJsonDto.getSkuName();
                    if (StrUtil.isNotEmpty(skuName)) {
                        if (skuName.contains(GoodsConstant.SEMICOLON)) {
                            //数据为 颜色分类--驼色  ;  尺码--S
                            String[] skuNameList = skuName.split(GoodsConstant.SEMICOLON);
                            for (String s : skuNameList) {
                                String[] split = s.split("--");
                                specs.add(split[1]);
                            }
                        } else {
                            //数据为 颜色分类--驼色
                            String[] split = skuName.split("--");
                            specs.add(split[1]);
                        }
                    }
                    skuDTO.setSpecs(specs);
                    skuDTO.setImage(tbSkuJsonDto.getImage());
                    skuDTO.setPrice(Double.parseDouble(tbSkuJsonDto.getPrice()));
                    skuDTO.setSalePrice(Double.parseDouble(tbSkuJsonDto.getPrice()));
                    skuDTO.setInitStock(tbSkuJsonDto.getQuantity());
                    skuDTO.setInitSalesVolume(0);
                    skuDTO.setLimitType(LimitType.UNLIMITED);
                    skuDTO.setStockType(StockType.LIMITED);
                    skuDTO.setLimitNum(0);
                    skuDTO.setWeight(BigDecimal.ZERO);
                    return skuDTO;
                }).toList();
        productDTO.setSkus(skuDTOList);
        //处理规格组
        List<SpecGroupDTO> specGroups = tbItemJsonDto.getProps().stream().map(tbItemPropsDto -> {
            List<SpecDTO> specDtoList = tbItemPropsDto.getValues().stream().map(tbItemPropsValues -> {
                SpecDTO specDTO = new SpecDTO();
                specDTO.setName(tbItemPropsValues.getName());
                return specDTO;
            }).toList();
            SpecGroupDTO specGroupDTO = new SpecGroupDTO();
            specGroupDTO.setName(tbItemPropsDto.getName());
            specGroupDTO.setChildren(specDtoList);
            return specGroupDTO;
        }).toList();
        productDTO.setSpecGroups(specGroups);
    }

    /**
     * 处理图片，图片没有请求协议的添加请求协议
     *
     * @param pic 图片
     * @return 处理后的图片
     */
    public String handlerPic(String pic) {
        if (StrUtil.isEmpty(pic)) {
            return GoodsConstant.DEFAULT_PIC;
        }
        String[] split = pic.split("//");
        return "https".equals(split[0]) ? pic : "https://" + split[1];
    }
}
