package com.medusa.gruul.goods.service.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
* 供应商商户
*
* <AUTHOR> 
*/
@Data
public class SupplierMerchantDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 应用id
     */
    private String appId;
    /**
     * 商户id，商户号，合作伙伴id等等
     */
    private String mchId;
    /**
     * 当私钥公钥为证书类型的时候，这里必填，可选值:PATH,STR,INPUT_STREAM,CLASS_PATH,URL
     */
    private String certStoreType;
    /**
     * 私钥或私钥证书
     */
    private String keyPrivate;
    /**
     * 公钥或公钥证书
     */
    private String keyPublic;
    /**
     * key证书,附加证书使用，如SSL证书，或者银联根级证书方面
     */
    private String keyCert;
    /**
     * 私钥证书或key证书的密码
     */
    private String keyCertPwd;
    /**
     * 异步回调
     */
    private String notifyUrl;
    /**
     * 同步回调地址，大部分用于付款成功后页面转跳
     */
    private String returnUrl;
    /**
     * 签名方式,MD5,RSA等等
     */
    private String signType;
    /**
     * 编码类型，如utf-8
     */
    private String inputCharset;
    /**
     * 主体名称
     */
    private String subjectName;
    /**
     * 是否为测试环境: 0 否，1 测试环境
     */
    private Integer isTest;
    /**
     * 请求api根域名
     */
    private String requestDomainUrl;
}