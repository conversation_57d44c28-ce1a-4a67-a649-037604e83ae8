package com.medusa.gruul.goods.service.controller.manager;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.common.idem.annotation.Idem;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.goods.api.entity.SupplierProduct;
import com.medusa.gruul.goods.api.model.dto.SupplierDTO;
import com.medusa.gruul.goods.api.model.dto.SupplierProductDTO;
import com.medusa.gruul.goods.api.model.vo.ProductVO;
import com.medusa.gruul.goods.service.mp.service.ISupplierProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 供应商产品控制器
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@PreAuthorize("@S.shopPerm('goods:list','goods:supplier')")
@RequestMapping("/supplier/product")
public class SupplierProductController {

    private final ISupplierProductService supplierProductService;


    /**
     * 分页查询产品
     *
     * @param page 查询条件
     * @return 分页产品
     */
    @GetMapping
    @Log("分页查询产品")
    public Result<IPage<SupplierProduct>> orderPage(Page<Void> page) {
        IPage<SupplierProduct> orderPage = supplierProductService.page(new Page<>(page.getCurrent(), page.getSize()));
        return Result.ok(
                orderPage
        );
    }

    @GetMapping("/getProductBySupplierId")
    @Log("根据供应商查产品")
    public Result<List<SupplierProduct>> getProductBySupplierId(Long supplierId) {
        List<SupplierProduct> supplierProductlist = supplierProductService.lambdaQuery().eq(SupplierProduct::getSupplierId, supplierId).list();
        return Result.ok(supplierProductlist);
    }


    /**
     * 获取产品详情
     *
     * @param id 产品id
     * @return 产品详情
     */
    @Log("获取产品详情")
    @GetMapping("{id}")
    public Result<SupplierProductDTO> productInfo(@PathVariable("id") @NotNull Long id) {
        SupplierProduct product = supplierProductService.getById(id);
        SupplierProductDTO supplierProductDTO = BeanUtil.copyProperties(product, SupplierProductDTO.class);
        return Result.ok(supplierProductDTO);
    }

    /**
     * 供应商产品删除
     *
     * @param ids 供应商ids
     */
    @Log("供应商产品删除")
    @DeleteMapping("/delete/{ids}")
    public Result<Void> deleteList(@PathVariable(name = "ids") Long[] ids) {
//        supplierProductService.removeByIds(ids);
        return Result.ok();
    }

    /**
     * 供应商产品新增
     *
     * @param productDTO 新增产品DTO
     */
    @Idem(1000)
    @Log("供应商产品新增")
    @PostMapping("/save")
    public Result<Void> addSupplier(@RequestBody @Validated SupplierProductDTO productDTO) {
        SupplierProduct supplierProduct = BeanUtil.copyProperties(productDTO, SupplierProduct.class);
        supplierProductService.save(supplierProduct);
        return Result.ok();
    }

    /**
     * 供应商产品修改
     *
     * @param productDTO 修改产品DTO
     */
    @Idem(1000)
    @Log("供应商产品修改")
    @PutMapping("/update")
    public Result<Void> updateSupplier(@RequestBody SupplierProductDTO productDTO) {
        SupplierProduct supplierProduct = BeanUtil.copyProperties(productDTO, SupplierProduct.class);
        supplierProductService.updateById(supplierProduct);
        return Result.ok();
    }

}
