package com.medusa.gruul.goods.service.mp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.goods.api.entity.SupplierGoodsSku;
import com.medusa.gruul.goods.api.model.dto.SupplierGoodsSkuDTO;
import com.medusa.gruul.goods.api.model.dto.SupplierGoodsSpecSkuDTO;
import com.medusa.gruul.goods.api.model.vo.SupplierGoodsSkuVO;

import java.util.List;

/**
 *
 * 供应商获得金额 服务类
 *
 * <AUTHOR>
 * @since 2022-03-04
 */
public interface ISupplierGoodsSkuService extends IService<SupplierGoodsSku> {
    /**
     * 查询单个sku价格信息
     *
     * @param skuId  商品skuid
     * @return 商品sku 价格信息
     */
    SupplierGoodsSkuVO getSupperSkuBySkuId(Long skuId, Long shopId);
    /**
     * 查询产品所有sku价格信息
     *
     * @param productId  产品id
     * @return 商品sku 价格信息
     */
    List<SupplierGoodsSkuVO> getSupperSkuByProductId(Long productId, Long shopId);
    /**
     * 创建/更新供应商获得金额
     * @param supplierGoodsSpecSkuDTO
     * @return
     */
    void saveOrUpdateSupperSku(SupplierGoodsSpecSkuDTO supplierGoodsSpecSkuDTO);
}
