

package com.medusa.gruul.goods.service;

import com.medusa.gruul.global.model.constant.AspectOrder;
import com.medusa.gruul.goods.service.properties.CopyGoodsProperties;
import com.medusa.gruul.goods.service.properties.GoodsConfigurationProperties;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;

/**
 * @Description: 商品服务
 * @Author: xiaoq
 * @Date: 2022/03/2 22:34
 */
@SpringBootApplication
@EnableCaching(order = AspectOrder.CACHE_ASPECT)
@EnableConfigurationProperties({GoodsConfigurationProperties.class, CopyGoodsProperties.class})
@EnableDubbo(scanBasePackages = "com.medusa.gruul.goods.service.service.impl")
public class GoodsApplication {

    public static void main(String[] args) {
        SpringApplication.run(GoodsApplication.class, args);
    }

}