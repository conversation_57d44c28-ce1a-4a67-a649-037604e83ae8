package com.medusa.gruul.goods.service.mp.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.common.custom.aggregation.classify.dto.CategoryRankDTO;
import com.medusa.gruul.common.model.base.ShopProductKey;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.entity.ProductCategory;
import com.medusa.gruul.goods.api.model.dto.YihuProductQueryDTO;
import com.medusa.gruul.goods.api.model.param.*;
import com.medusa.gruul.goods.api.model.vo.*;
import com.medusa.gruul.goods.service.model.param.PurchaseProductParam;
import com.medusa.gruul.goods.service.model.param.SupplierProductParam;
import com.medusa.gruul.goods.service.model.vo.ProductNumVo;
import com.medusa.gruul.goods.service.model.vo.ProductStatusQuantityVO;
import com.medusa.gruul.goods.service.model.vo.SupplierIssueProductListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 商品信息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-03-04
 */
public interface ProductMapper extends BaseMapper<Product> {


    /**
     * 获取商品详细信息
     *
     * @param id 商品id
     * @return 单个商品详细信息
     */
    ProductVO getProductById(@Param("id") Long id);


    /**
     * 获取商品信息list
     *
     * @param productParam 商品信息查询参数
     * @return 商品list信息
     */
    IPage<ProductVO> queryProductList(@Param("productParam") ProductParam productParam);


    /**
     * productInfoByParam
     *
     * @param platformProductParam 查询条件
     * @return PlatformProductVo
     */
    @InterceptorIgnore(tenantLine = "true")
    Page<PlatformProductVO> queryProductInfoByParam(@Param("platformProductParam") PlatformProductParam platformProductParam);

    /**
     * 供应商商品信息
     *
     * @param supplierProductParam 供应商商品查询信息
     * @return IPage(供应商商品基础信息)
     */
    IPage<Product> getSupplierProductList(@Param("supplierProductParam") SupplierProductParam supplierProductParam);


    /**
     * 根据平台类目id 获取所对应得商品信息
     *
     * @param platformCategoryParam 分页
     * @param levelCategoryList     levelCategoryList
     * @return 查询结果
     */
    @InterceptorIgnore(tenantLine = "true")
    Page<ApiPlatformProductVO> queryProductInfoByPlatformCategoryIds(@Param("platformCategoryParam") PlatformCategoryParam platformCategoryParam, @Param("levelCategoryList") List<Long> levelCategoryList);

    /**
     * 获取店铺商品基础信息 By apiProductParam
     *
     * @param apiProductParam 查询条件
     * @return Page<ApiProductVo>
     */
    Page<ApiProductVO> getProductInfoByParam(@Param("apiProductParam") ApiProductParam apiProductParam);


    /**
     * 根据平台三级类目id 获取商品信息list
     *
     * @param platformCategoryParam 分页param
     * @return Page<ApiPlatformProductVO>
     */

    Page<ApiPlatformProductVO> getProductInfoByPlatformThirdlyCategoryId(@Param("platformCategoryParam") PlatformCategoryParam platformCategoryParam);

    /**
     * x
     *
     * @param categoryId 类目id
     * @return ProductCategory.java
     */
    ProductCategory queryProductCategory(Long categoryId);


    /**
     * 根据平台三级类目获取 对应的商品数量
     *
     * @param thirdIds 平台三级类目ids
     * @return Map<平台类目id, 商品数量>
     */
    List<ProductNumVo> getProductNumByPlatformThirdCategoryId(@Param("thirdIds") Set<Long> thirdIds);

    /**
     * 获取随机商品
     *
     * @param productRandomParam 参数
     * @return 商品
     */
    Page<Product> randomGoods(@Param("productRandomParam") ProductRandomParam productRandomParam);

    /**
     * 获取商品数量 by status
     *
     * @return ProductStatusQuantityVO.java
     */
    List<ProductStatusQuantityVO> queryGoodsQuantity();

    /**
     * 查询今日新增商品数量
     *
     * @param shopId 店铺id
     * @return 新增商品数量
     */
    Long queryTodayAddGoodsQuantity(@Param("shopId") Long shopId);

    /**
     * 根据三级类目ids 获取商品基础信息
     *
     * @param categoryRank 查询条件
     * @return
     */
    Page<ApiProductVO> getProductInfoByCategoryId(@Param("categoryRank") CategoryRankDTO categoryRank);


    /**
     * 根据平台三级类目ids 获取  ApiProductVO
     *
     * @param categoryRank 类目等级dto
     * @return ApiProductVO
     */
    Page<ApiProductVO> getApiProductInfoByPlatformCategoryId(@Param("categoryRank") CategoryRankDTO categoryRank);

    /**
     * 获取条件商品信息 包含以删除商品信息
     *
     * @param shopId    店铺id
     * @param productId 商品id
     * @return 商品信息
     */
    Product getConditionProductInfo(@Param("shopId") Long shopId, @Param("productId") Long productId);


    /**
     * 批量查询商品信息
     *
     * @param shopProductKeys ShopProductKey
     * @return List<Product>  商品信息
     */
    List<Product> getProductBatch(@Param("shopProductKeys") Set<ShopProductKey> shopProductKeys);


    /**
     * 查看签约类目下是否存在商品
     *
     * @param signingCategorySecondIds 签约类目二级ids
     * @param shopId                   店铺id
     * @return 是否存在
     */
    boolean querySigningCategoryProduct(@Param("signingCategorySecondIds") Set<Long> signingCategorySecondIds, @Param("shopId") Long shopId);


    /**
     * 根据商品id 获取商品信息
     *
     * @param productId     商品id
     * @param isQueryDelete 是否查询删除商品
     * @param shopId        店铺id
     * @return 商品信息
     */
    Product getProductInfoById(@Param("productId") Long productId, @Param("isQueryDelete") Boolean isQueryDelete, @Param("shopId") Long shopId);

    /**
     * 根据商品id 获取商品信息
     *
     * @param supplierId    供应商id
     * @param productId     商品id
     * @param isQueryDelete 是否查询删除商品
     * @return 商品信息
     */
    List<Product> getProductBySupplierIdAndProductId(@Param("supplierId") Long supplierId, @Param("productId") Long productId, @Param("isQueryDelete") Boolean isQueryDelete);


    /**
     * 获取商品基础信息
     *
     * @param param 检索条件
     * @return IPage<ProductStockVO>
     */
    IPage<ProductStockVO> queryProductStockBaseInfo(@Param("param") ProductStockParam param);


    /**
     * 获取采购发布商品信息
     *
     * @param param 查询param
     * @return IPage<SupplierIssueProductListVO>
     */
    IPage<SupplierIssueProductListVO> queryPurchaseIssueProducts(@Param("param") PurchaseProductParam param);

    /**
     * 已铺货代销商品
     *
     * @param purchaseProductParam 查询参数
     * @return IPag<已铺货代销商品信息>
     */
    IPage<SupplierIssueProductListVO> getPaveGoods(@Param("param") PurchaseProductParam purchaseProductParam);

    /**
     * 根据商品id集合查询 商品信息(包含已逻辑删除)
     *
     * @param hashProductId keys是否包含商品 id 否则 只是用 shopId 作为供应商 id 查询
     * @param keys          商品id + 店铺id
     * @return 商品信息
     */
    Set<ShopProductKey> queryGoodsByProductIds(@Param("hashProductId") boolean hashProductId, @Param("keys") Set<ShopProductKey> keys);

    /**
     * 强删商品
     *
     * @param shopProductKeys 商品id + 店铺id
     */
    void supplierForceGoodsStatus(@Param("shopProductKeys") Set<ShopProductKey> shopProductKeys);

    /**
     * 修改店铺供应商商品
     *
     * @param id     产品id
     * @param shopId 店铺id
     */
    void updateSupplierSellGoods(@Param("id") Long id, @Param("shopId") Long shopId);

    /**
     * 根据shopId查询商品信息（首页渲染使用）
     * @param productRenderParam
     * @return
     */
    IPage<ProductRenderVO> queryProductRenderVO(@Param("productRenderParam") ProductRenderParam productRenderParam);

    /**
     * 查询店铺下所有产品，包括已删除
     * @param shopId
     * @return
     */
    List<Product> getProductListByShopId(@Param("shopId") Long shopId);




    /**
     * 医护用户获取多产品信息
     * @param yihuProductQueryDTO
     * @return
     */
    List<ProductVO> getProductByName(@Param("params") YihuProductQueryDTO yihuProductQueryDTO);

}
