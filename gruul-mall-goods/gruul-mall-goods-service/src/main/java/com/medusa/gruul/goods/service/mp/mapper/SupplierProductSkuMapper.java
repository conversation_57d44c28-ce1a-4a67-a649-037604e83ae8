package com.medusa.gruul.goods.service.mp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.goods.api.entity.SupplierProductSku;
import com.medusa.gruul.goods.api.model.vo.SupplierProductSkuVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 供应商产品sku关联
*
* <AUTHOR> 
*/
public interface SupplierProductSkuMapper extends BaseMapper<SupplierProductSku> {
    /**
     * 查询供应商产品
     * @param productId 店铺产品id
     * @return 供应商产品
     */
    List<SupplierProductSkuVO> getSupplierSkuByProductId(@Param("productId") Long productId);
    /**
     * 查询供应商产品
     * @param skuId 店铺产品skuid
     * @return 供应商产品
     */
    SupplierProductSkuVO getSupplierSkuBySkuId(@Param("skuId") Long skuId);
}