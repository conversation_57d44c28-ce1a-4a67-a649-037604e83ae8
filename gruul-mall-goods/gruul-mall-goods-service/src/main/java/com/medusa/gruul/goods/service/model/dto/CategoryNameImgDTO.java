package com.medusa.gruul.goods.service.model.dto;

import com.medusa.gruul.common.web.valid.group.AddGroup;
import com.medusa.gruul.common.web.valid.group.UpdateGroup;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * date 2022/4/24
 */
@Getter
@Setter
@ToString
public class CategoryNameImgDTO {

    /**
     * 分类名称
     */
    @NotBlank(groups = {AddGroup.class, UpdateGroup.class})
    private String name;

    /**
     * 图片
     */
    private String categoryImg;
}
