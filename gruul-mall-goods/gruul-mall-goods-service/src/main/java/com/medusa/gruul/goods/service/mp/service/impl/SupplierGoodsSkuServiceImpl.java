package com.medusa.gruul.goods.service.mp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.goods.api.entity.SupplierGoodsSku;
import com.medusa.gruul.goods.api.model.dto.SupplierGoodsSkuDTO;
import com.medusa.gruul.goods.api.model.dto.SupplierGoodsSpecSkuDTO;
import com.medusa.gruul.goods.api.model.vo.SupplierGoodsSkuVO;
import com.medusa.gruul.goods.service.mp.mapper.SupplierGoodsSkuMapper;
import com.medusa.gruul.goods.service.mp.service.ISupplierGoodsSkuService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 供应商获得金额 服务类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SupplierGoodsSkuServiceImpl extends ServiceImpl<SupplierGoodsSkuMapper, SupplierGoodsSku> implements ISupplierGoodsSkuService {


    @Override
    public SupplierGoodsSkuVO getSupperSkuBySkuId(Long skuId, Long shopId) {
        SupplierGoodsSku supplierGoodsSku = ISystem.shopId(shopId, () ->
                this.lambdaQuery()
                        .eq(SupplierGoodsSku::getSkuId, skuId)
                        .one()
        );
        if (null == supplierGoodsSku) {
            return null;
        }
        return BeanUtil.copyProperties(supplierGoodsSku, SupplierGoodsSkuVO.class);
    }

    @Override
    public List<SupplierGoodsSkuVO> getSupperSkuByProductId(Long productId, Long shopId) {
        List<SupplierGoodsSku> supplierGoodsSkuList = ISystem.shopId(shopId, () ->
                this.lambdaQuery()
                        .eq(SupplierGoodsSku::getProductId, productId)
                        .list()
        );
        if (null == supplierGoodsSkuList) {
            return Lists.newArrayList();
        }
        return BeanUtil.copyToList(supplierGoodsSkuList, SupplierGoodsSkuVO.class);
    }

    @Override
    public void saveOrUpdateSupperSku(SupplierGoodsSpecSkuDTO supplierGoodsSpecSkuDTO) {
        Long shopId = ISecurity.shopIdOrISysMust();
        //判断是否是新商品
        boolean newSku = !this.lambdaQuery()
                .eq(SupplierGoodsSku::getShopId, shopId)
                .eq(SupplierGoodsSku::getProductId, supplierGoodsSpecSkuDTO.getProductId())
                .exists();
        //如果是新商品 保存规格与规格组
        if (newSku) {
            this.saveNewSkus(supplierGoodsSpecSkuDTO.getSupplierGoodsSkus());
        } else {
            this.editOldSkus(supplierGoodsSpecSkuDTO.getSupplierGoodsSkus());
        }
    }

    /**
     * 保存
     */
    private void saveNewSkus(List<SupplierGoodsSkuDTO> supplierGoodsSkuDTOList) {
        if (CollUtil.isEmpty(supplierGoodsSkuDTOList)) {
            return;
        }
        List<SupplierGoodsSku> supplierGoodsSkus = BeanUtil.copyToList(supplierGoodsSkuDTOList, SupplierGoodsSku.class);
        boolean success = this.saveBatch(supplierGoodsSkus);
        SystemCode.DATA_ADD_FAILED.falseThrow(success);
    }

    /**
     * 编辑
     *
     * @param supplierGoodsSkuDTOList
     */
    @SuppressWarnings({"unchecked"})
    private void editOldSkus(List<SupplierGoodsSkuDTO> supplierGoodsSkuDTOList) {
        List<SupplierGoodsSku> supplierGoodsSkus = BeanUtil.copyToList(supplierGoodsSkuDTOList, SupplierGoodsSku.class);
        supplierGoodsSkus.forEach(supplierGoodsSku -> {
            SupplierGoodsSku sku = this.lambdaQuery()
                    .eq(SupplierGoodsSku::getSkuId, supplierGoodsSku.getSkuId())
                    .eq(SupplierGoodsSku::getShopId, supplierGoodsSku.getShopId())
                    .one();
            if (null != sku) {
                supplierGoodsSku.setId(sku.getId());
            }
        });
        boolean success = this.saveOrUpdateBatch(supplierGoodsSkus);
        SystemCode.DATA_ADD_FAILED.falseThrow(success);
    }


}
