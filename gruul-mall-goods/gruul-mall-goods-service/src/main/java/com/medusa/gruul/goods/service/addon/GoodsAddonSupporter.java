package com.medusa.gruul.goods.service.addon;

import com.medusa.gruul.common.addon.supporter.annotation.AddonMethod;
import com.medusa.gruul.common.addon.supporter.annotation.AddonSupporter;
import com.medusa.gruul.common.model.base.ShopProductKey;
import com.medusa.gruul.goods.api.model.dto.ProductDTO;
import com.medusa.gruul.goods.api.model.dto.SupplierDTO;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import com.medusa.gruul.goods.api.model.vo.DistributeProductVO;
import com.medusa.gruul.goods.api.model.vo.PlatformCategoryVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 商品服务提供插件支持器
 *
 * <AUTHOR>
 * @Description GoodsAddonSupporter.java
 * @date 2022-09-17 09:30
 */
@AddonSupporter(id = "goodsAddonSupporter")
public interface GoodsAddonSupporter {

    /**
     * 获取商品状态 addonSupporter
     *
     * @param status 商品状态
     * @return list<商品状态>
     */
    @AddonMethod(returnType = ProductStatus.class)
    ProductStatus getProductStatus(ProductStatus status);

    /**
     * 根据三级类目id获取对应的一级、二级类目id
     *
     * @param platformCategoryIdSet 三级类目id
     * @return List<三级类目>
     */
    @AddonMethod(returnType = PlatformCategoryVo.class)
    PlatformCategoryVo getPlatformCategoryVoByLevel3Id(Set<Long> platformCategoryIdSet);

    /**
     * 获取平台店铺签约类目自定义扣率
     *
     * @param platformTwoCategory 平台二级类目
     * @param shopId              店铺id
     * @return 签约类目自定义扣率
     */
    @AddonMethod(returnType = Long.class)
    Long getCustomDeductionRatio(Long platformTwoCategory, Long shopId);


    /**
     * 采购商品发布
     *
     * @param productId  商品id
     * @param shopId     店铺id
     * @param supplierId 供应id
     */
    @AddonMethod(returnType = void.class)
    void purchaseProductIssue(Long productId, Long shopId, Long supplierId);

    /**
     * 统计所有供应商商品数据
     *
     * @return {@link Map}
     */
    @AddonMethod(returnType = Map.class)
    Map<String, Integer> countProductsOfAllSupplier();

    /**
     * 代销商品获取
     *
     * @param shopProductKeys 代销商品 唯一sky
     * @return List<ProductDTO>
     */
    @AddonMethod(returnType = List.class)
    List<ProductDTO> getSupplierGoods(Set<ShopProductKey> shopProductKeys);

    /**
     * 根据订单号查询通知请求参数
     *
     * @param orderNo 订单号
     * @return json
     */
    @AddonMethod(returnType = String.class)
    String getSendParamByOrderNo(String orderNo);

    /**
     * 获取分销产品
     * @param productIds 产品id集合
     * @return
     */
    @AddonMethod(returnType = List.class)
    List<DistributeProductVO> getDistributeProductList(Set<Long> productIds);
}
