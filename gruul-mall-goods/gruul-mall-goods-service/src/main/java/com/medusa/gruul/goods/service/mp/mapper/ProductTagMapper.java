package com.medusa.gruul.goods.service.mp.mapper;

import com.medusa.gruul.goods.api.entity.ProductTag;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface ProductTagMapper extends BaseMapper<ProductTag> {
 void deleteProductTagsInIds(@Param("ids") Set ids);
}
