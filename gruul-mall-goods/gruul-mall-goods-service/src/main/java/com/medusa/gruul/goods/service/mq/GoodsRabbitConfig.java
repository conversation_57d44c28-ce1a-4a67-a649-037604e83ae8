package com.medusa.gruul.goods.service.mq;

import com.medusa.gruul.goods.api.enums.GoodsRabbit;
import com.medusa.gruul.order.api.enums.OrderRabbit;
import com.medusa.gruul.shop.api.enums.ShopRabbit;
import org.springframework.amqp.core.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * 队列交换机绑定
 *
 * <AUTHOR>
 * description 队列交换机绑定
 * date 2022-06-24 09:31
 */
@Configuration
public class GoodsRabbitConfig {


    /**
     * 创建交换机
     */
    @Bean
    @ConditionalOnMissingBean(name = "goodsExchange")
    public Exchange goodsExchange() {
        return new DirectExchange(GoodsRabbit.EXCHANGE, true, false);
    }

    /**
     * 店铺交换机
     */
    @Bean
    @ConditionalOnMissingBean(name = "shopExchange")
    public Exchange shopExchange() {
        return new DirectExchange(ShopRabbit.EXCHANGE, true, false);
    }

    /**
     * 订单交换机
     */
    @Bean
    @ConditionalOnMissingBean(name = "orderExchange")
    public Exchange orderExchange() {
        return ExchangeBuilder.directExchange(OrderRabbit.ORDER_ACCOMPLISH.exchange())
                .durable(true)
                .delayed().build();
    }

    /**
     * 店铺状态改变 启用禁用队列
     *
     * @return Queue
     */
    @Bean
    public Queue shopChangeQueue() {
        return new Queue(GoodsRabbitQueueNames.SHOP_CHANGE_QUEUE, true);
    }

    @Bean
    public Binding shopExchangeBinding() {
        return BindingBuilder
                .bind(shopChangeQueue())
                .to(shopExchange())
                .with(ShopRabbit.SHOP_ENABLE_DISABLE.routingKey())
                .noargs();
    }


    /**
     * ---------------------------------------订单完成 ---------------------------------
     */
//    @Bean
//    public Queue orderAccomplishQueue() {
//        return QueueBuilder.durable(GoodsRabbitQueueNames.ORDER_ACCOMPLISH_QUEUE)
//                .build();
//    }
//
//
//    @Bean
//    public Binding orderAccomplishBinding() {
//        return BindingBuilder
//                .bind(orderAccomplishQueue())
//                .to(orderExchange())
//                .with(OrderRabbit.ORDER_ACCOMPLISH.routingKey())
//                .noargs();
//    }

    /**
     * ---------------------------------------订单评价完成 ---------------------------------
     */
    @Bean
    public Queue orderCommentAccomplishQueue() {
        return QueueBuilder.durable(GoodsRabbitQueueNames.ORDER_COMMENT_ACCOMPLISH_QUEUE)
                .build();
    }


    @Bean
    public Binding orderCommentAccomplishBinding() {
        return BindingBuilder
                .bind(orderCommentAccomplishQueue())
                .to(orderExchange())
                .with(OrderRabbit.ORDER_COMMENT_ACCOMPLISH.routingKey())
                .noargs();
    }

    /**
     * ---------------------------------------店铺更新 ---------------------------------
     */
    @Bean
    public Queue goodsShopInfoUpdateQueue() {
        return QueueBuilder.durable(GoodsRabbitQueueNames.SHOP_INFO_UPDATE_QUEUE)
                .build();
    }

    @Bean
    public Binding goodsShopInfoUpdateBinding() {
        return BindingBuilder
                .bind(goodsShopInfoUpdateQueue())
                .to(shopExchange())
                .with(ShopRabbit.SHOP_UPDATE.routingKey())
                .noargs();
    }


    /**
     * ---------------------------------------供应商商品状态更新 ---------------------------------
     */
    @Bean
    public Queue supplierGoodsUpdateStatusQueue() {
        return QueueBuilder.durable(GoodsRabbitQueueNames.SUPPLIER_GOODS_UPDATE_STATUS_QUEUE)
                .build();
    }

    @Bean
    public Binding supplierGoodsUpdateStatusBinding() {
        return BindingBuilder
                .bind(supplierGoodsUpdateStatusQueue())
                .to(goodsExchange())
                .with(GoodsRabbit.SUPPLIER_GOODS_UPDATE_STATUS.routingKey())
                .noargs();
    }


    /**
     * ---------------------------------------供应商商品删除---------------------------------
     */
    @Bean
    public Queue supplierForceGoodsStatusQueue() {
        return QueueBuilder.durable(GoodsRabbitQueueNames.SUPPLIER_FORCE_GOODS_STATUS_QUEUE)
                .build();
    }

    @Bean
    public Binding supplierForceGoodsStatusBinding() {
        return BindingBuilder
                .bind(supplierForceGoodsStatusQueue())
                .to(goodsExchange())
                .with(GoodsRabbit.SUPPLIER_FORCE_GOODS_STATUS.routingKey())
                .noargs();
    }


    /**
     * ---------------------------------------供应商商品修改---------------------------------
     */
    @Bean
    public Queue supplierUpdateGoodsQueue() {
        return QueueBuilder.durable(GoodsRabbitQueueNames.SUPPLIER_UPDATE_GOODS)
                .build();
    }

    @Bean
    public Binding supplierUpdateGoodsQueueBinding() {
        return BindingBuilder
                .bind(supplierUpdateGoodsQueue())
                .to(goodsExchange())
                .with(GoodsRabbit.SUPPLIER_UPDATE_GOODS.routingKey())
                .noargs();
    }

}
