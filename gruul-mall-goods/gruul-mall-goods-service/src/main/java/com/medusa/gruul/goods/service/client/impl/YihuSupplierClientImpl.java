package com.medusa.gruul.goods.service.client.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.goods.api.entity.SupplierMerchant;
import com.medusa.gruul.goods.api.entity.SupplierRequestRecord;
import com.medusa.gruul.goods.api.model.param.NotifyYHParam;
import com.medusa.gruul.goods.service.client.YihuSupplierClient;
import com.medusa.gruul.goods.service.mp.mapper.SupplierRequestRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 医护供应商接口
 */
@Slf4j
@Component
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class YihuSupplierClientImpl implements YihuSupplierClient {

    @Autowired
    private SupplierRequestRecordMapper supplierRequestRecordDao;
    //发卡
    private final String BUSINESS_SAVE = "/api/yihutech/business/save";
    //获取token
    private final String THIRD_TOKEN = "/api/user/login/third/getToken";
    //销卡
    private final String BUSINESS_CANCEL = "/api/yihutech/business/cancel";

    //续购校验
    private final String REPURCHASE_CHECK = "/api/healthbusiness/butRight/getInfo";

    private final String NOTIFY_REPURCHASESUCCESS = "/api/order/yhShopBuyRightTimes";
    //退权益
    private final String BUSINESS_CANCEL_RIGHT = "/api/order/yhShopBuyRightTimesRefund";

    @Override
    public Result<Object> createCard(SupplierMerchant supplierMerchant, Map<String, Object> params) {
        Result<Object> result = Result.ok();
        //签名
        sign(supplierMerchant, params);
        //请求记录
        SupplierRequestRecord requestRecordEntity = new SupplierRequestRecord();
        requestRecordEntity.setSupplierId(supplierMerchant.getSupplierId());
        requestRecordEntity.setRequestUrl(convertRequestUrl(supplierMerchant.getRequestDomainUrl(), BUSINESS_SAVE));
        requestRecordEntity.setRequestNo(params.get("businessNo").toString());
        requestRecordEntity.setSendParam(JSONUtil.toJsonStr(params));
        if (params.containsKey("orderNo")) {
            requestRecordEntity.setOrderNo(params.get("orderNo").toString());
        }
        supplierRequestRecordDao.insert(requestRecordEntity);
        //调用接口
        try {
            log.debug("调用医护供应商创建卡接口，请求参数：{}", requestRecordEntity.getSendParam());
            String str = HttpUtil.post(requestRecordEntity.getRequestUrl(),
                    requestRecordEntity.getSendParam());
            log.debug("调用医护供应商创建卡接口成功，返回结果：{}", str);
            requestRecordEntity.setNotifyParam(str);
            result = JSONUtil.toBean(str, Result.class);
        } catch (Exception e) {
            result.setCode(500);
            result.setMsg("权益开通失败");
            if (null == requestRecordEntity.getSendParam()) {
                requestRecordEntity.setNotifyParam(e.getMessage());
            }
            log.error("调用医护供应商创建卡接口失败，e: {}, requestRecordEntity:{}", e.getMessage(), JSONUtil.toJsonStr(requestRecordEntity));
        } finally {
            requestRecordEntity.setUpdateTime(LocalDateTime.now());
            supplierRequestRecordDao.updateById(requestRecordEntity);
        }
        return result;
    }

    @Override
    public Result<Object> cancelCard(SupplierMerchant supplierMerchant, Map<String, Object> params) {
        Result<Object> result = Result.ok();
        //签名
        sign(supplierMerchant, params);
        //请求记录
        SupplierRequestRecord requestRecordEntity = new SupplierRequestRecord();
        requestRecordEntity.setSupplierId(supplierMerchant.getSupplierId());
        requestRecordEntity.setRequestUrl(convertRequestUrl(supplierMerchant.getRequestDomainUrl(), BUSINESS_CANCEL));
        requestRecordEntity.setRequestNo(params.get("businessNo").toString());
        requestRecordEntity.setSendParam(JSONUtil.toJsonStr(params));
        if (params.containsKey("orderNo")) {
            requestRecordEntity.setOrderNo(params.get("orderNo").toString());
        }
        supplierRequestRecordDao.insert(requestRecordEntity);
        //调用接口
        try {
            log.debug("调用医护供应商销卡接口，请求参数：{}", requestRecordEntity.getSendParam());
            String str = HttpUtil.post(requestRecordEntity.getRequestUrl(),
                    requestRecordEntity.getSendParam());
            log.debug("调用医护供应商销卡接口成功，返回结果：{}", str);
            requestRecordEntity.setNotifyParam(str);
            result = JSONUtil.toBean(str, Result.class);
        } catch (Exception e) {
            result.setCode(500);
            result.setMsg("销卡失败");
            if (null == requestRecordEntity.getSendParam()) {
                requestRecordEntity.setNotifyParam(e.getMessage());
            }
            log.error("调用医护供应商销卡接口失败，e: {}, requestRecordEntity:{}", e.getMessage(), JSONUtil.toJsonStr(requestRecordEntity));
        } finally {
            requestRecordEntity.setUpdateTime(LocalDateTime.now());
            supplierRequestRecordDao.updateById(requestRecordEntity);
        }
        return result;
    }

    @Override
    public Result<Object> cancelCardRight(SupplierMerchant supplierMerchant, NotifyYHParam notifyYHParam) {
        Result<Object> result = Result.ok();
        notifySign(supplierMerchant, notifyYHParam);
        String requestUrl = convertRequestUrl(supplierMerchant.getRequestDomainUrl(), BUSINESS_CANCEL_RIGHT);
        //请求记录
        SupplierRequestRecord requestRecordEntity = new SupplierRequestRecord();
        requestRecordEntity.setSupplierId(supplierMerchant.getSupplierId());
        requestRecordEntity.setRequestUrl(requestUrl);
        requestRecordEntity.setRequestNo(UUID.randomUUID().toString(true));
        requestRecordEntity.setSendParam(JSONUtil.toJsonStr(notifyYHParam));
        requestRecordEntity.setOrderNo(notifyYHParam.getOutTradeNo());
        supplierRequestRecordDao.insert(requestRecordEntity);
        try {
            log.debug("调用续购退款接口,参数为：{}", notifyYHParam);

            String resultStr = HttpUtil.post(requestUrl, JSONObject.from(notifyYHParam).toJSONString());
            requestRecordEntity.setNotifyParam(resultStr);
            result = JSONUtil.toBean(resultStr, Result.class);
        } catch (Exception e) {
            if (null == requestRecordEntity.getSendParam()) {
                requestRecordEntity.setNotifyParam(e.getMessage());
            }
            log.error("调用续购退款接口失败,e：{}", e.getMessage());
        } finally {
            requestRecordEntity.setUpdateTime(LocalDateTime.now());
            supplierRequestRecordDao.updateById(requestRecordEntity);
        }
        return result;
    }

    @Override
    public String getToken(SupplierMerchant supplierMerchant, String userId) {
        //先从缓存获取
        String supplierTokenKey = RedisUtil.key(supplierMerchant.getSupplierId().toString(), userId);
        Object redisToken = RedisUtil.getCacheObject(supplierTokenKey);
        if (null != redisToken) {
            return redisToken.toString();
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("userId", userId);
        params.put("appCode", supplierMerchant.getAppId());
        //签名
        signToken(supplierMerchant, params);
        //请求记录
        SupplierRequestRecord requestRecordEntity = new SupplierRequestRecord();
        requestRecordEntity.setSupplierId(supplierMerchant.getSupplierId());
        requestRecordEntity.setRequestUrl(convertRequestUrl(supplierMerchant.getRequestDomainUrl(), THIRD_TOKEN));
        requestRecordEntity.setRequestNo(UUID.randomUUID().toString(true));
        requestRecordEntity.setSendParam(JSONUtil.toJsonStr(params));
        supplierRequestRecordDao.insert(requestRecordEntity);
        //调用接口
        try {
            log.debug("调用医护供应商获取token接口，请求参数：{}", requestRecordEntity.getSendParam());
            String str = HttpUtil.post(requestRecordEntity.getRequestUrl(),
                    requestRecordEntity.getSendParam());
            log.debug("调用医护供应商获取token成功，返回结果：{}", str);
            requestRecordEntity.setNotifyParam(str);
            Result<Object> result = JSONUtil.toBean(str, Result.class);
            if (result.getCode() != 0) {
                throw new RuntimeException(result.getMsg());
            }
            Map<String, Object> map = BeanUtil.beanToMap(result.getData());
            //set缓存, 23小时过期
            RedisUtil.setCacheObject(supplierTokenKey, map.get("token").toString(), 60 * 60 * 23, TimeUnit.SECONDS);
            return map.get("token").toString();
        } catch (Exception e) {
            if (null == requestRecordEntity.getSendParam()) {
                requestRecordEntity.setNotifyParam(e.getMessage());
            }
            log.error("调用医护供应商token接口失败，e: {}, requestRecordEntity:{}", e.getMessage(), JSONUtil.toJsonStr(requestRecordEntity));
        } finally {
            requestRecordEntity.setUpdateTime(LocalDateTime.now());
            supplierRequestRecordDao.updateById(requestRecordEntity);
        }
        return null;
    }

    private void sign(SupplierMerchant supplierMerchant, Map<String, Object> params) {
        String singStr = "appCode=" + supplierMerchant.getAppId() + "&appkey=" + supplierMerchant.getKeyPublic() + "&businessNo=" + params.get("businessNo");
        //签名
        String sign = SecureUtil.md5(singStr).toUpperCase();
        params.put("sign", sign);
    }

    private void signToken(SupplierMerchant supplierMerchant, Map<String, Object> params) {
        String singStr = "appCode=" + supplierMerchant.getAppId() + "&appkey=" + supplierMerchant.getKeyPublic() + "&userId=" + params.get("userId");
        //签名
        String sign = SecureUtil.md5(singStr).toUpperCase();
        params.put("sign", sign);
    }

    /**
     * 通知医护签名
     *
     * @param supplierMerchant 供应商配置
     * @param notifyYHParam
     */
    private void notifySign(SupplierMerchant supplierMerchant, NotifyYHParam notifyYHParam) {
        String singStr = "appCode=" + supplierMerchant.getAppId() + "&appkey=" + supplierMerchant.getKeyPublic() + "&thirdOrderId=" + notifyYHParam.getThirdOrderId() + "&timestamp=" + notifyYHParam.getTimestamp();
        //签名
        String sign = SecureUtil.md5(singStr).toUpperCase();
        notifyYHParam.setSign(sign);
    }

    /**
     * 续购校验签名
     *
     * @param supplierMerchant 供应商配置
     * @param
     * @return
     */
    private void checkSign(SupplierMerchant supplierMerchant, Map<String, Object> params) {
        String singStr = "appCode=" + supplierMerchant.getAppId() + "&appkey=" + supplierMerchant.getKeyPublic() + "&userRightId=" + params.get("userRightId") + "&timestamp=" + params.get("timestamp");
        //签名
        String sign = SecureUtil.md5(singStr).toUpperCase();
        params.put("sign", sign);
    }

    @Override
    public void repurchaseCheck(SupplierMerchant supplierMerchant, Map<String, Object> repurchaseCheckParam) {
        checkSign(supplierMerchant, repurchaseCheckParam);
        String requestUrl = convertRequestUrl(supplierMerchant.getRequestDomainUrl(), REPURCHASE_CHECK);
        //请求记录
        SupplierRequestRecord requestRecordEntity = new SupplierRequestRecord();
        requestRecordEntity.setSupplierId(supplierMerchant.getSupplierId());
        requestRecordEntity.setRequestUrl(convertRequestUrl(supplierMerchant.getRequestDomainUrl(), REPURCHASE_CHECK));
        requestRecordEntity.setRequestNo(UUID.randomUUID().toString(true));
        requestRecordEntity.setSendParam(JSONUtil.toJsonStr(repurchaseCheckParam));
        supplierRequestRecordDao.insert(requestRecordEntity);
        try {
            log.debug("调用续购校验接口,参数为：{}", repurchaseCheckParam);
            String post = HttpUtil.post(requestUrl, JSON.toJSONString(repurchaseCheckParam));
            Result result = JSONUtil.toBean(post, Result.class);
            requestRecordEntity.setNotifyParam(post);
            if (result.getCode() != 0) {
                throw new GlobalException(result.getMsg());
            }
            if(result.getCode()==0){
                JSONObject resultData = JSONObject.from(result.getData());
                Integer aloneBuyCount = Integer.valueOf(String.valueOf(resultData.get("aloneBuyCount")));
                if(aloneBuyCount<-1||aloneBuyCount==0){
                    throw new GlobalException("已无法购买,您的续购次数不足!");
                }
            }

        } catch (Exception e) {
            if (null == requestRecordEntity.getSendParam()) {
                requestRecordEntity.setNotifyParam(e.getMessage());
            }
            log.error("调用续购校验接口失败,e：{}", e.getMessage());
            throw new GlobalException("校验不通过,请重试或联系客服！");
        } finally {
            requestRecordEntity.setUpdateTime(LocalDateTime.now());
            supplierRequestRecordDao.updateById(requestRecordEntity);
        }
    }

    public Result<Object> notifyYHRepurchaseSuccess(SupplierMerchant supplierMerchant, NotifyYHParam notifyYHParam) {
        notifySign(supplierMerchant, notifyYHParam);
        String requestUrl = convertRequestUrl(supplierMerchant.getRequestDomainUrl(), NOTIFY_REPURCHASESUCCESS);
        //请求记录
        SupplierRequestRecord requestRecordEntity = new SupplierRequestRecord();
        requestRecordEntity.setSupplierId(supplierMerchant.getSupplierId());
        requestRecordEntity.setRequestUrl(convertRequestUrl(supplierMerchant.getRequestDomainUrl(), NOTIFY_REPURCHASESUCCESS));
        requestRecordEntity.setRequestNo(UUID.randomUUID().toString(true));
        requestRecordEntity.setSendParam(JSONUtil.toJsonStr(notifyYHParam));
        requestRecordEntity.setOrderNo(notifyYHParam.getOutTradeNo());
        supplierRequestRecordDao.insert(requestRecordEntity);
        try {
            log.debug("调用续购通知接口,参数为：{}", notifyYHParam);

            String result = HttpUtil.post(requestUrl, JSONObject.from(notifyYHParam).toJSONString());
            requestRecordEntity.setNotifyParam(result);
            return JSONUtil.toBean(result, Result.class);

        } catch (Exception e) {
            if (null == requestRecordEntity.getSendParam()) {
                requestRecordEntity.setNotifyParam(e.getMessage());
            }
            log.error("调用续购通知接口失败,e：{}", e.getMessage());
        } finally {
            requestRecordEntity.setUpdateTime(LocalDateTime.now());
            supplierRequestRecordDao.updateById(requestRecordEntity);
        }
        return null;
    }
}
