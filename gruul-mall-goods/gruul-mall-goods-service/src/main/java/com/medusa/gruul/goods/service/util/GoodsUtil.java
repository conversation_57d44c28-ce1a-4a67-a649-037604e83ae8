package com.medusa.gruul.goods.service.util;

import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.goods.api.constant.GoodsConstant;

/**
 * <AUTHOR>
 * date 2022/5/18
 */
public final class GoodsUtil {

    private GoodsUtil() {
    }

    /**
     * 获取缓存 商品缓存 key
     *
     * @param shopId    店铺id
     * @param productId 商品id
     * @return key
     */
    public static String productCacheKey(Long shopId, Long productId) {
        return RedisUtil.key(GoodsConstant.GOODS_DETAIL_CACHE_KEY, shopId, productId);
    }

    /**
     * @param shopId 店铺id
     * @return key
     */
    public static String productCacheKeyPattern(Long shopId) {
        return RedisUtil.key(GoodsConstant.GOODS_DETAIL_CACHE_KEY, shopId, "*");
    }
}
