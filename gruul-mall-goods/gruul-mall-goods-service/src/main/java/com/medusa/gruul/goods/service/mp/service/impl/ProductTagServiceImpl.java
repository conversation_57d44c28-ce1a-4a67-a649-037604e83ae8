package com.medusa.gruul.goods.service.mp.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.goods.api.entity.ProductTag;
import com.medusa.gruul.goods.api.model.dto.ProductTagDTO;
import com.medusa.gruul.goods.service.mp.mapper.ProductTagMapper;
import com.medusa.gruul.goods.service.mp.service.IProductTagService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Service
public class ProductTagServiceImpl extends ServiceImpl<ProductTagMapper, ProductTag> implements IProductTagService {

    @Override
    public IPage<ProductTag> queryProductTagByParam(ProductTagDTO productTagDTO) {
        return this.lambdaQuery()
                .eq(StrUtil.isNotBlank(productTagDTO.getTagName()), ProductTag::getTagName, productTagDTO.getTagName())
                .eq(productTagDTO.getShopId()!=null,ProductTag::getShopId,productTagDTO.getShopId())
                .orderByAsc(ProductTag::getSort)
                .page(productTagDTO);
    }

    @Override
    public void saveProductTag(ProductTagDTO productTagDTO) {
        SecureUser secureUser = ISecurity.userMust();
        productTagDTO.setShopId(secureUser.getShopId());
        ProductTag productTag = new ProductTag();
        BeanUtils.copyProperties(productTagDTO, productTag, ProductTag.class);
        this.save(productTag);
    }

    @Override
    public void updateProductTag(ProductTagDTO productTagDTO) {
        this.lambdaUpdate()
                .eq(ProductTag::getId,productTagDTO.getId())
                .set(ProductTag::getTagName, productTagDTO.getTagName())
                .set(ProductTag::getSort, productTagDTO.getSort())
                .set(ProductTag::getUpdateTime, LocalDateTime.now())
                .update();
    }

    @Override
    public void deleteProductTag(Set<Long> ids) {
        baseMapper.deleteProductTagsInIds(ids);
    }


}
