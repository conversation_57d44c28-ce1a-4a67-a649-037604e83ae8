package com.medusa.gruul.goods.service.mp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.goods.api.entity.SupplierProductSku;
import com.medusa.gruul.goods.api.model.vo.SupplierProductSkuVO;

import java.util.List;

/**
 *
 * 供应商产品 服务类
 *
 *
 * <AUTHOR>
 */
public interface ISupplierProductSkuService extends IService<SupplierProductSku> {
    /**
     * 查询供应商产品
     * @param productId
     * @return
     */
    List<SupplierProductSkuVO> getSupplierSkuByProductId(Long productId);
    /**
     * 查询供应商产品
     * @param skuId
     * @return
     */
    SupplierProductSkuVO getSupplierSkuBySkuId(Long skuId);

}
