package com.medusa.gruul.goods.service.model.dto;

import com.medusa.gruul.goods.api.model.enums.ProductType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class YihuProductDTO {
    /**
     * skuNo
     */
    private String no;
    /**
     * 图片
     */
    private String pic;
    /**
     * 销售价
     */
    private Long salePrice;
    /**
     * 库存类型 1 无限库存 2 有限
     */
    private Integer stockType;
    /**
     * 库存数量
     */
    private Integer stock;
    /**
     * 规格
     */
    private List<String> specs;
    /**
     * 商品名称
     */
    private String name;
    /**
     * 上下架状态
     */
    private Integer status;


    /**
     * 商品id
     */
    private Long productId;
    /**
     * skuId
     */
    private Long id;

    /**
     * 商品类型
     */
    private ProductType productType;



}
