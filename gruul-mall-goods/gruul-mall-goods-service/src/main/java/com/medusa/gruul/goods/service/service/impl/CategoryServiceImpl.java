package com.medusa.gruul.goods.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.common.custom.aggregation.classify.dto.CategoryRankDTO;
import com.medusa.gruul.common.custom.aggregation.classify.enums.CategoryLevel;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.entity.ProductCategory;
import com.medusa.gruul.goods.api.model.enums.GoodsError;
import com.medusa.gruul.goods.api.model.vo.ProductCategoryLevel1WithNumVO;
import com.medusa.gruul.goods.service.functions.CategoryQueryFunction;
import com.medusa.gruul.goods.service.model.dto.CategorySortDTO;
import com.medusa.gruul.goods.service.model.dto.ProductCategoryDTO;
import com.medusa.gruul.goods.service.mp.service.IProductCategoryService;
import com.medusa.gruul.goods.service.mp.service.IProductService;
import com.medusa.gruul.goods.service.service.CategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * date 2022/4/21
 */
@Service
@RequiredArgsConstructor
public class CategoryServiceImpl implements CategoryService {

    private final IProductService productService;
    private final CategoryQueryFunction categoryQueryFunction;
    private final IProductCategoryService productCategoryService;
    @Override
    public IPage<ProductCategory> pageProductCategory(Page<Void> page) {
        /* 一级类目分页查询
         */
        IPage<ProductCategory> categoriesPage = productCategoryService.lambdaQuery()
                .eq(ProductCategory::getParentId, CommonPool.NUMBER_ZERO.longValue())
                .eq(ProductCategory::getLevel, CategoryLevel.LEVEL_1)
                .orderByAsc(ProductCategory::getSort)
                .page(new Page<>(page.getCurrent(), page.getSize()));

        /* 查询出的一级分类列表
         */
        List<ProductCategory> categories = categoriesPage.getRecords();
        if (CollUtil.isEmpty(categories)) {
            return categoriesPage;
        }
        /* 二级分类列表
         */
        categories = categoryQueryFunction.setChildren(categories, CategoryLevel.LEVEL_2);
        /* 三级分类列表
         */
        if (CollUtil.isEmpty(categories)) {
            return categoriesPage;
        }
        categoryQueryFunction.setChildren(categories, CategoryLevel.LEVEL_3);
        return categoriesPage;
    }

    @Override
    public IPage<ProductCategory> pageProductCategoryByParentId(Long parentId, Page<Void> page) {
        return productCategoryService.lambdaQuery()
                .eq(ProductCategory::getParentId, parentId)
                .page(
                        new Page<>(page.getCurrent(), page.getSize())
                );
    }

    @Override
    public void newProductCategory(ProductCategoryDTO category) {
        category.paramCheck();
        category.save(productCategoryService);
    }

    @Override
    public void editProductCategory(Long categoryId, ProductCategoryDTO category) {
        category.paramCheck();
        category.update(productCategoryService, categoryId);
    }

    @Override
    public void deleteProductCategory(Long categoryId) {
        ProductCategory category = productCategoryService.getById(categoryId);
        SystemCode.DATA_NOT_EXIST.trueThrow(category == null);
        /* 查询所有子分类id (包含自身id)
         */
        Set<Long> categoryIds = categoryQueryFunction.categoryIdAndChildrenIds(categoryId, category.getLevel());
        /* 判断分类id列表下是否有绑定商品
         */
        boolean exists = productService.lambdaQuery()
                .in(Product::getCategoryId, categoryIds)
                .exists();
        GoodsError.ATTRIBUTES_REPETITION.trueThrow(exists);
        /* 删除数据
         */
        boolean success = productCategoryService.lambdaUpdate()
                .in(ProductCategory::getId, categoryIds)
                .remove();
        SystemCode.DATA_UPDATE_FAILED.falseThrow(success);
    }

    @Override
    public void sortCategories(CategorySortDTO categorySort) {
        List<Long> sortedIds = categorySort.getSortedIds();

        List<ProductCategory> categories = productCategoryService.lambdaQuery()
                .in(ProductCategory::getId, sortedIds)
                .eq(ProductCategory::getParentId, categorySort.getParentId())
                .last("ORDER BY FIELD (id," + sortedIds.stream().map(String::valueOf).collect(Collectors.joining(",")) + ")")
                .list();
        IntStream.range(0, categories.size()).forEach(
                index -> categories.get(index).setSort(index)
        );
        productCategoryService.updateBatchById(categories);
    }

    /**
     * 查询一级分类和一级分类下的商品数量
     *
     * @param page page
     * @return 分类和分类下商品数量
     */
    @Override
    public IPage<ProductCategoryLevel1WithNumVO> pageCategoryLevel1WithProductNum(Page<Void> page) {

        List<ProductCategory> level3List = productCategoryService.lambdaQuery()
                .eq(ProductCategory::getLevel, CategoryLevel.LEVEL_3)
                .list();

        Map<Long, List<ProductCategory>> secondCategoryList = level3List.stream().collect(Collectors.groupingBy(ProductCategory::getParentId));
        Page<ProductCategory> level1Page = productCategoryService.lambdaQuery()
                .eq(ProductCategory::getLevel, CategoryLevel.LEVEL_1)
                .orderByDesc(ProductCategory::getSort)
                .page(new Page<>(page.getCurrent(), page.getSize()));
        List<ProductCategory> level1List = level1Page.getRecords();

        List<ProductCategory> level2List = productCategoryService.lambdaQuery()
                .eq(ProductCategory::getLevel, CategoryLevel.LEVEL_2)
                .list();
        Map<Long, List<ProductCategory>> firstCategoryList = level2List.stream().collect(Collectors.groupingBy(ProductCategory::getParentId));

        List<ProductCategoryLevel1WithNumVO> productCategoryLevel1WithNumVos = CollUtil.emptyIfNull(level1List).stream().map(level1 -> {
            AtomicReference<Long> count = new AtomicReference<>(0L);
            // 二级分类集合
            List<ProductCategory> level2CategoryList = firstCategoryList.get(level1.getId());
            if (CollUtil.isNotEmpty(level2CategoryList)) {
                level2CategoryList.forEach(level2 -> {
                    // 三级分类集合id
                    List<ProductCategory> level3Category = secondCategoryList.get(level2.getId());
                    if (CollUtil.isNotEmpty(level3Category)) {
                        List<Long> level3IdList = level3Category.stream().map(ProductCategory::getId).collect(Collectors.toList());
                        Long num = productService.lambdaQuery()
                                .in(Product::getCategoryId, level3IdList)
                                .count();
                        count.updateAndGet(v -> v + num);
                    }

                });
            }
            ProductCategoryLevel1WithNumVO productCategoryLevel1WithNumVO = new ProductCategoryLevel1WithNumVO();
            productCategoryLevel1WithNumVO.setId(level1.getId())
                    .setName(level1.getName())
                    .setProductNum(count.get());
            return productCategoryLevel1WithNumVO;
        }).collect(Collectors.toList());
        IPage<ProductCategoryLevel1WithNumVO> iPage = new Page<>();
        BeanUtils.copyProperties(level1Page, iPage);
        iPage.setRecords(productCategoryLevel1WithNumVos);
        return iPage;
    }

    /**
     * 根据一级ids 获取 一级类目下的类目信息
     *
     * @param categoryRank 类目级别dto
     * @return List<ProductCategory>
     */
    @Override
    public List<ProductCategory> getCategoryInfoByIds(CategoryRankDTO categoryRank) {
        List<ProductCategory> categories = productCategoryService.lambdaQuery()
                .eq(ProductCategory::getParentId, CommonPool.NUMBER_ZERO.longValue())
                .eq(ProductCategory::getLevel, CategoryLevel.LEVEL_1)
                .last("ORDER BY FIELD (id," + categoryRank.getIds().stream().map(String::valueOf).collect(Collectors.joining(",")) + ")")
                .in(BaseEntity::getId, categoryRank.getIds()).list();
        /* 查询出的一级分类列表
         */
        if (CollUtil.isEmpty(categories)) {
            return categories;
        }
        /* 二级分类列表
         */
        List<ProductCategory> categoriesSecond = categoryQueryFunction.setChildren(categories, CategoryLevel.LEVEL_2);
        /* 三级分类列表
         */
        if (CollUtil.isEmpty(categoriesSecond)) {
            return categories;
        }

        if (categoryRank.getCategoryLevel() == CategoryLevel.LEVEL_3) {
            categoryQueryFunction.setChildren(categoriesSecond, CategoryLevel.LEVEL_3);
        }
        // 处理"为你推荐"分类
        if (categoryRank.getIsShowRecommendForYou()!=null) {
            if (categoryRank.getIsShowRecommendForYou()==1) {
                ProductCategory recommendCategory = createRecommendForYouCategory();
                categories.add(0, recommendCategory); // 添加到列表首位
            }
        }
        return categories;
    }

    /**
     * 创建"为你推荐"分类
     */
    private ProductCategory createRecommendForYouCategory() {
        ProductCategory recommendCategory = new ProductCategory();
        recommendCategory.setId(0L);
        recommendCategory.setName("为你推荐");
        recommendCategory.setParentId(0L);
        recommendCategory.setLevel(CategoryLevel.LEVEL_1);
        recommendCategory.setSort(0);
        recommendCategory.setCreateTime(LocalDateTime.now());
        recommendCategory.setUpdateTime(LocalDateTime.now());
        recommendCategory.setVersion(0);
        recommendCategory.setDeleted(false);

        // 创建二级分类"电脑"
        ProductCategory computerCategory = new ProductCategory();
        computerCategory.setId(0L); // 使用实际ID
        computerCategory.setName("推荐");
        computerCategory.setParentId(0L); // 父ID指向"为你推荐"分类
        computerCategory.setLevel(CategoryLevel.LEVEL_2);
        computerCategory.setSort(0);
        computerCategory.setCreateTime(LocalDateTime.now());
        computerCategory.setUpdateTime(LocalDateTime.now());
        computerCategory.setVersion(0);
        computerCategory.setDeleted(false);
        computerCategory.setCategoryImg("https://whealthcn-**********.cos.ap-shanghai.myqcloud.com/testmedilifemall/20241114/903552016ef246cc85cc00d195c5dea8.jpg");
        // 将"电脑"分类添加为"为你推荐"的子分类
        recommendCategory.setChildren(Collections.singletonList(computerCategory));
        return recommendCategory;
    }


}
