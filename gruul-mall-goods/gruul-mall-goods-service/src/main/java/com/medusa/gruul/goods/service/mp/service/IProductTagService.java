package com.medusa.gruul.goods.service.mp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.goods.api.entity.ProductTag;
import com.medusa.gruul.goods.api.model.dto.ProductTagDTO;

import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface IProductTagService extends IService<ProductTag> {
    /**
     *查询产品标签列表
     * @param productTagDTO 参数
     * @return
     */
    IPage<ProductTag> queryProductTagByParam(ProductTagDTO productTagDTO);

    /**
     * 保存产品标签
     * @param productTagDTO DTO
     */

    void saveProductTag(ProductTagDTO productTagDTO);

    /**
     * 更新产品标签
     * @param productTagDTO
     */

    void updateProductTag(ProductTagDTO productTagDTO);

    /**
     * 根据id集合删除产品标签
     * @param ids
     */

    void deleteProductTag(Set<Long> ids);


}
