package com.medusa.gruul.goods.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.model.dto.YihuProductQueryDTO;
import com.medusa.gruul.goods.service.model.dto.YihuProductDTO;
import com.medusa.gruul.goods.service.mp.service.IProductService;
import com.medusa.gruul.goods.service.service.YihuProductService;
import com.medusa.gruul.storage.api.entity.StorageSku;
import com.medusa.gruul.storage.api.rpc.StorageRpcService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class YihuProductServiceImpl implements YihuProductService {
    private final IProductService productService;
    private final StorageRpcService storageRpcService;

    @Override
    public List<YihuProductDTO> getProductsByName(YihuProductQueryDTO yihuProductQueryDTO) {
        if (yihuProductQueryDTO.getNos() == null || CollUtil.isEmpty(yihuProductQueryDTO.getNos())) {
            return Collections.EMPTY_LIST;
        }

        List<StorageSku> storageSpecSkuByNos = storageRpcService.getStorageSpecSkuByNos(yihuProductQueryDTO.getNos());
        if (CollUtil.isEmpty(storageSpecSkuByNos)) {
            return Collections.EMPTY_LIST;
        }
        Set<Long> productIds = storageSpecSkuByNos.stream().map(StorageSku::getProductId).collect(Collectors.toSet());
        List<Product> products = TenantShop.disable(() -> productService.lambdaQuery().select(Product::getId, Product::getStatus, Product::getProductType, Product::getName)
                .eq(Product::getShopId, yihuProductQueryDTO.getShopId())
                .in(Product::getId, productIds)
                .like(StrUtil.isNotBlank(yihuProductQueryDTO.getName()), Product::getName, yihuProductQueryDTO.getName()).list());
        if (CollUtil.isEmpty(products)) {
            return Collections.EMPTY_LIST;
        }
        List<Long> dbProductIds = products.stream().map(Product::getId).collect(Collectors.toList());
        List<YihuProductDTO> yihuProductDTOs = new ArrayList<>();
        storageSpecSkuByNos.stream()
                .filter(storageSku->dbProductIds.contains(storageSku.getProductId()))
                .forEach(sku -> {
            YihuProductDTO yihuProductDTO = new YihuProductDTO();
            BeanUtils.copyProperties(sku, yihuProductDTO);
            yihuProductDTO.setSpecs(sku.getSpecs())
                    .setStockType(sku.getStockType().getValue())
                    .setPic(sku.getImage())
                    .setProductType(null)
                    .setStock(sku.getStock().intValue());
            yihuProductDTOs.add(yihuProductDTO);
        });

        yihuProductDTOs.stream().forEach(yihuProductDTO -> {
            products.stream().forEach(product -> {
                if (yihuProductDTO.getProductId().equals(product.getId())) {
                    yihuProductDTO.setName(product.getName())
                            .setStatus(product.getStatus().getStatus())
                            .setProductType(product.getProductType());
                }
            });
        });
        return yihuProductDTOs;
    }
}
