package com.medusa.gruul.goods.service.properties;


import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 *
 * Properties配置
 * <AUTHOR>
 *
 */

@Data
@ConfigurationProperties(prefix = "gruul.goods")
public class GoodsConfigurationProperties {


    /**
     * 线程池配置
     */
    private TaskThreadPool threadPool = new TaskThreadPool();


    @Getter
    @Setter
    public static class TaskThreadPool {

        /**
         * 线程池线程名前缀
         */
        private String threadNamePrefix = "Good-Future";

        /**
         * 核心线程数 - 适合4核CPU的I/O密集型任务
         */
        private int corePoolSize = 8;

        /**
         * 最大线程数 - 处理突发流量
         */
        private int maxPoolSize = 16;

        /**
         * 线程存活时间长度 - 空闲线程保持时间(秒)
         */
        private int keepAliveSeconds = 60;

        /**
         * 任务队列长度
         */
        private int queueCapacity = 1000;
    }

}
