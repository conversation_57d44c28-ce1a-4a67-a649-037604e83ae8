package com.medusa.gruul.goods.service.properties;


import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 *
 * Properties配置
 * <AUTHOR>
 *
 */

@Data
@ConfigurationProperties(prefix = "gruul.goods")
public class GoodsConfigurationProperties {


    /**
     * 线程池配置
     */
    private TaskThreadPool threadPool = new TaskThreadPool();


    @Getter
    @Setter
    public static class TaskThreadPool {

        /**
         * 线程池线程名前缀
         */
        private String threadNamePrefix = "Good-Future";

        /**
         * 核心线程数
         */
        private int corePoolSize = 10;

        /**
         * 最大线程数
         */
        private int maxPoolSize = 25;

        /**
         * 线程存活时间长度
         */
        private int keepAliveSeconds = 60;

        /**
         * 任务队列长度
         */
        private int queueCapacity = 800;
    }

}
