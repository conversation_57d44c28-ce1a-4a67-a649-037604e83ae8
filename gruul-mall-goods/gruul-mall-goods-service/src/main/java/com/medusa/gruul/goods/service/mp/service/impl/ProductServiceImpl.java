package com.medusa.gruul.goods.service.mp.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.medusa.gruul.common.custom.aggregation.classify.dto.CategoryRankDTO;
import com.medusa.gruul.common.fastjson2.FastJson2;
import com.medusa.gruul.common.model.base.ActivityShopProductKey;
import com.medusa.gruul.common.model.base.ShopProductKey;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.enums.OrderType;
import com.medusa.gruul.common.model.enums.SellType;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.mp.IManualTransaction;
import com.medusa.gruul.common.mp.config.MybatisPlusConfig;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.common.mp.model.SqlHelper;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.common.mp.page.PageBean;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.global.model.enums.Mode;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.global.model.o.Final;
import com.medusa.gruul.goods.api.constant.GoodsConstant;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.entity.ProductCategory;
import com.medusa.gruul.goods.api.entity.SupplierProductSku;
import com.medusa.gruul.goods.api.entity.SupplierRateRecord;
import com.medusa.gruul.goods.api.enums.GoodsRabbit;
import com.medusa.gruul.goods.api.model.CategoryLevel;
import com.medusa.gruul.goods.api.model.dto.*;
import com.medusa.gruul.goods.api.model.enums.GoodsError;
import com.medusa.gruul.goods.api.model.enums.PricingType;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import com.medusa.gruul.goods.api.model.enums.ProductType;
import com.medusa.gruul.goods.api.model.param.*;
import com.medusa.gruul.goods.api.model.vo.*;
import com.medusa.gruul.goods.api.rpc.GoodsRpcService;
import com.medusa.gruul.goods.service.addon.GoodsAddonSupporter;
import com.medusa.gruul.goods.service.model.ProductConstant;
import com.medusa.gruul.goods.service.model.dto.ConsignmentProductDTO;
import com.medusa.gruul.goods.service.model.dto.ShopProductSkuIdDTO;
import com.medusa.gruul.goods.service.model.param.PurchaseProductParam;
import com.medusa.gruul.goods.service.model.vo.ProductDeliveryVO;
import com.medusa.gruul.goods.service.model.vo.ProductNumVo;
import com.medusa.gruul.goods.service.model.vo.ProductStatusQuantityVO;
import com.medusa.gruul.goods.service.model.vo.SupplierIssueProductListVO;
import com.medusa.gruul.goods.service.mp.mapper.ProductMapper;
import com.medusa.gruul.goods.service.mp.service.IProductCategoryService;
import com.medusa.gruul.goods.service.mp.service.IProductService;
import com.medusa.gruul.goods.service.mp.service.ISupplierProductSkuService;
import com.medusa.gruul.goods.service.mp.service.ISupplierRateRecordService;
import com.medusa.gruul.goods.service.util.GoodsUtil;
import com.medusa.gruul.order.api.entity.OrderEvaluate;
import com.medusa.gruul.order.api.model.OrderCompletedDTO;
import com.medusa.gruul.order.api.rpc.OrderRpcService;
import com.medusa.gruul.shop.api.enums.OperaReason;
import com.medusa.gruul.shop.api.enums.ShopStatus;
import com.medusa.gruul.shop.api.model.dto.ShopsEnableDisableDTO;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import com.medusa.gruul.storage.api.dto.ShopProductKeyDTO;
import com.medusa.gruul.storage.api.dto.SkuDTO;
import com.medusa.gruul.storage.api.dto.StorageSpecSkuDTO;
import com.medusa.gruul.storage.api.entity.StorageSku;
import com.medusa.gruul.storage.api.rpc.StorageRpcService;
import com.medusa.gruul.storage.api.vo.ProductSaleVolumeVO;
import com.medusa.gruul.storage.api.vo.ProductSkusVO;
import com.medusa.gruul.storage.api.vo.ProductStatisticsVO;
import com.medusa.gruul.user.api.rpc.UserRpcService;
import io.vavr.Tuple;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Supplier;
import java.util.stream.Collectors;


/**
 * 商品信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-04
 */
@Service
@RequiredArgsConstructor
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements IProductService {

    private final Executor goodsExecutor;
    private final IProductCategoryService productCategoryService;
    private final RabbitTemplate rabbitTemplate;
    private final GoodsAddonSupporter goodsAddonSupporter;
    private final OrderRpcService orderRpcService;
    private final ISupplierRateRecordService supplierRateRecordService;
    private final WxMaService wxMaService;
    @Value("${spring.profiles.active}")
    private String profileActive;
    private ShopRpcService shopRpcService;
    private UserRpcService userRpcService;
    private StorageRpcService storageRpcService;
    private GoodsRpcService goodsRpcService;
    private ISupplierProductSkuService supplierProductSkuService;
    private static final String QR_CODE_URL_TEMPLATE = "/pluginPackage/goods/commodityInfo/InfoEntranceShop?shopId=%s&goodId=%s";

    public byte[] createWxMaQrcode(String path) throws WxErrorException {
        WxMaCodeLineColor wxMaCodeLineColor = new WxMaCodeLineColor();
        wxMaCodeLineColor.setR("255");
        wxMaCodeLineColor.setG("105");
        wxMaCodeLineColor.setB("46");
        //release 生产。 trial 测试
        return wxMaService.getQrcodeService().createWxaCodeBytes(path, "prod".equals(profileActive) ? "release" : "trial", 1280, false, wxMaCodeLineColor, false);
    }

    @Override
    public void generateQRCoded(QRCodeDTO qrCodeDTO, HttpServletResponse response) {
        String url = String.format(QR_CODE_URL_TEMPLATE, qrCodeDTO.getShopId(), qrCodeDTO.getGoodId());
        try {
            byte[] wxMaQrcode = createWxMaQrcode(url);
            response.setContentType("image/jpeg");
            response.setHeader("Content-Disposition", "attachment; filename=\"qrcode.jpg\"");
            response.setContentLengthLong(wxMaQrcode.length);
            try (OutputStream out = response.getOutputStream()) {
                out.write(wxMaQrcode);
                out.flush();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 商品发布
     *
     * @param productDto 商品信息Dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void issueProduct(ProductDTO productDto) {
        Long shopId = ISecurity.shopIdOrISysMust();

        Mode mode = shopRpcService.getShopInfoByShopId(shopId).getMode();
        productDto.checkDistributionMode(mode);
        // 渲染product商品基础信息
        Product product = productDto.coverProduct()
                .setShopId(shopId);
        if (product.getScore() == null) {
            product.setScore(BigDecimal.valueOf(5));
        }
        SellType sellType = productDto.getSellType();
        if (sellType != SellType.OWN) {
            product.setId(productDto.getId());
            product.setSupplierId(productDto.getSupplierId());
        }
        // 扩展信息
        setExtendInfo(product, productDto.getPlatformCategory(), productDto.getShopCategory(), productDto.getProductAttributes(), productDto.getProductParameters(), productDto.getConsignmentPriceSetting());

        List<SkuDTO> skus = productDto.getSkus();
        List<Long> sortedSalePrices = skus.stream()
                .map(SkuDTO::getSalePrice)
                .sorted()
                .collect(Collectors.toList());
        product.setStatus(ProductStatus.SELL_ON);
        product.setPic(StrUtil.split(product.getAlbumPics(), StrPool.COMMA).get(CommonPool.NUMBER_ZERO));
        product.setStatus(productDto.getStatus());
        product.setSharePic(productDto.getSharePic());
        /* 非自有商品处理
         */
        if (SellType.OWN != sellType && productDto.getId() != null) {
            product.setId(productDto.getId());
            product.setSupplierId(productDto.getSupplierId());
            if (SellType.CONSIGNMENT == sellType) {
                product.getExtra().setSupplierCustomDeductionRatio(productDto.getSupplierCustomDeductionRatio());
                ConsignmentPriceSettingDTO consignmentPriceSetting = productDto.getConsignmentPriceSetting();
                //代销商品修改商品价格
                boolean isRegular = consignmentPriceSetting.getType() == PricingType.REGULAR;
                sortedSalePrices = sortedSalePrices.stream()
                        .map(sortedSalePrice -> sortedSalePrice + (isRegular
                                ? consignmentPriceSetting.getSale()
                                : (sortedSalePrice * consignmentPriceSetting.getSale() / 1000000))).toList();
            }
        }
        product.setSalePrice(sortedSalePrices.get(CommonPool.NUMBER_ZERO));
        product.setSalePrices(sortedSalePrices);
        product.setIsUserSearch(productDto.getIsUserSearch() == null ? Boolean.TRUE : productDto.getIsUserSearch());
        //商品编号
        product.setNo(StrUtil.isBlank(product.getNo()) ? RedisUtil.no(ProductConstant.NO).toString() : product.getNo());
        //商品信息落库
        int insert = this.baseMapper.insert(product);
        GoodsError.PRODUCT_ISSUE_FAIL.trueThrow(insert == 0);

        if (SellType.OWN != sellType) {
            //如果是代销商品则检查供应商是否可用
            if (SellType.CONSIGNMENT == sellType) {
                ShopInfoVO supplier = shopRpcService.getShopInfoByShopId(product.getSupplierId());
                GoodsError.SUPPLIER_HAVE_DELETE.trueThrow(supplier == null || ShopStatus.NORMAL != supplier.getStatus());
            }
            goodsAddonSupporter.purchaseProductIssue(product.getId(), shopId, product.getSupplierId());
        }

        // sku信息
        editProductSkuInfo(productDto, product, skus);
        editSupplierSkuInfo(productDto, product, skus);
        if (productDto.getProductType() == ProductType.REPURCHASE_PRODUCT) {
            return;
        }
        IManualTransaction.afterCommit(
                // 发送商品发布广播
                () -> sendProductBroadcast(productDto.getShopCategory(), productDto.getPlatformCategory(),
                        product, GoodsRabbit.GOODS_RELEASE.exchange(), GoodsRabbit.GOODS_RELEASE.routingKey())
        );
    }


    /**
     * 商品删除
     *
     * @param productIds 商品ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProductList(Set<Long> productIds) {
        Long shopId = ISecurity.shopIdOrISysMust();
        // 已下架的商品才能删除
        Optional<List<Product>> goodsListOptional = Optional.ofNullable(this.lambdaQuery()
                .eq(Product::getShopId, shopId)
                .in(Product::getStatus, Arrays.asList(ProductStatus.SELL_OFF, ProductStatus.PLATFORM_SELL_OFF, ProductStatus.SUPPLIER_SELL_OFF))
                .in(BaseEntity::getId, productIds).list());

        int size = goodsListOptional.map(List::size).orElse(CommonPool.NUMBER_ZERO);
        GoodsError.PRODUCT_CANNOT_BE_DELETED.trueThrow(productIds.size() != size);

        Set<String> cacheKeys = productIds.stream().map(productId -> GoodsUtil.productCacheKey(shopId, productId)).collect(Collectors.toSet());
        // 双删
        Boolean flag = RedisUtil.doubleDeletion(
                () -> removeByIds(productIds),
                () -> RedisUtil.delete(cacheKeys)
        );
        GoodsError.PRODUCT_DELETE_FAIL.falseThrow(flag);
        goodsExecutor.execute(
                () -> rabbitTemplate.convertAndSend(
                        GoodsRabbit.GOODS_DELETE.exchange(),
                        GoodsRabbit.GOODS_DELETE.routingKey(),
                        new ProductDeleteDTO()
                                .setShopId(shopId)
                                .setProductIds(productIds)
                )
        );
    }

    /**
     * 商品上下架 同步更新缓存
     *
     * @param productStatusChange 商品状态更改信息
     * @param status              产品上下架状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductStatus(ProductStatusChangeDTO productStatusChange, ProductStatus status) {
        Set<ShopProductKey> productKeys = productStatusChange.getKeys();
//        ISecurity.match()
//                .ifAnyShopAdmin((secureUser) -> productKeys.forEach(key -> key.setShopId(secureUser.getShopId())));
        productKeys.forEach(key -> key.setShopId(ISecurity.shopIdOrISysMust()));
        List<Product> currentProducts = TenantShop.disable(
                () -> {
                    LambdaQueryChainWrapper<Product> query = this.lambdaQuery();
                    productKeys.forEach(
                            key -> query.or(inner -> inner.eq(Product::getShopId, key.getShopId()).eq(BaseEntity::getId, key.getProductId()))
                    );
                    return query.list();
                }
        );
        if (CollUtil.isEmpty(currentProducts)) {
            return;
        }
        ProductViolationDTO productViolation = productStatusChange.getProductViolation();

        //排除 平台下架商品
        currentProducts = currentProducts.stream().filter(product -> product.getStatus() != ProductStatus.PLATFORM_SELL_OFF).collect(Collectors.toList());
        if (CollUtil.isEmpty(currentProducts)) {
            return;
        }
        // 转换成 店铺ID 对应 商品 id 集合对象列表
        List<ProductUpdateStatusDTO> updateStatusList = currentProducts.stream()
                .collect(Collectors.groupingBy(Product::getShopId))
                .entrySet()
                .stream()
                .peek(entry ->
                        entry.getValue()
                                .forEach(product -> {
                                    if (status == ProductStatus.SELL_ON && !validConsignmentProductStatus(product.getStatus())) {
                                        throw new GlobalException(SystemCode.PARAM_VALID_ERROR_CODE, "供应商下架与禁用的商品不能上架");
                                    }
                                })
                )
                .map(entry -> new ProductUpdateStatusDTO()
                        .setShopId(entry.getKey())
                        .setProductIds(entry.getValue().stream().map(Product::getId).collect(Collectors.toSet()))
                        .setProductStatus(status)
                ).collect(Collectors.toList());
        // 商品状态处理
        handlerGoodsStatus(status, productViolation, updateStatusList);
    }


    /**
     * 商品信息修改
     *
     * @param productDto 商品信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProduct(ProductDTO productDto) {
        Long shopId = ISecurity.shopIdOrISysMust();

        //校验mode
        Mode mode = shopRpcService.getShopInfoByShopId(shopId).getMode();
        productDto.checkDistributionMode(mode);

        //校验商品
        Product product = checkoutProductInfo(productDto.getId(), shopId, Boolean.TRUE);
        //商品基础信息修改
        Product newProduct = productDto.coverProduct().setShopId(shopId);

        //扩展信息处理
        setExtendInfo(newProduct, productDto.getPlatformCategory(), productDto.getShopCategory(), productDto.getProductAttributes(), productDto.getProductParameters(), productDto.getConsignmentPriceSetting());
        newProduct.setPic(StrUtil.split(newProduct.getAlbumPics(), StrPool.COMMA).get(CommonPool.NUMBER_ZERO));
        // 修改商品 不可更改项 取原商品信息 -- 商品类型 销售类型
        newProduct.setProductType(product.getProductType());
        newProduct.setSellType(product.getSellType());
        newProduct.setPlatformCategoryId(product.getPlatformCategoryId());
        List<SkuDTO> skus = productDto.getSkus();
        List<Long> sortedSalePrices = skus.stream().map(SkuDTO::getSalePrice).sorted().toList();
        newProduct.setSalePrice(sortedSalePrices.get(CommonPool.NUMBER_ZERO));
        newProduct.setSalePrices(sortedSalePrices);
        newProduct.setSharePic(productDto.getSharePic());
        newProduct.setNo(productDto.getNo());
        newProduct.setIsUserSearch(productDto.getIsUserSearch() == null ? product.getIsUserSearch() : productDto.getIsUserSearch());
        //设置编号
        if (StrUtil.isBlank(product.getNo()) || StrUtil.isBlank(newProduct.getNo())) {
            newProduct.setNo(RedisUtil.no(ProductConstant.NO).toString());
        }

        // 同步修改商品信息
        Integer update = RedisUtil.doubleDeletion(
                () -> baseMapper.update(newProduct, Wrappers.lambdaQuery(Product.class)
                        .eq(Product::getId, newProduct.getId())
                        .eq(Product::getShopId, newProduct.getShopId())),
                RedisUtil.key(GoodsConstant.GOODS_DETAIL_CACHE_KEY, shopId, product.getId()
                ));
        GoodsError.PRODUCT_UPDATE_FAIL.trueThrow(update < CommonPool.NUMBER_ONE);

        // sku信息
        editProductSkuInfo(productDto, product, skus);
        editSupplierSkuInfo(productDto, product, skus);
        //发送商品修改广播
        newProduct.setShopId(shopId).setCreateTime(product.getCreateTime());
        sendProductBroadcast(productDto.getShopCategory(), productDto.getPlatformCategory(), newProduct, GoodsRabbit.GOODS_UPDATE.exchange(), GoodsRabbit.GOODS_UPDATE.routingKey());
    }


    /**
     * 查询单个商品信息
     *
     * @param productId 商品id
     * @param shopId    店铺id
     * @param score     是否是有效的热度得分
     * @return 商品详情
     */
    @Override
    public ProductVO getProductById(Long productId, Long shopId, boolean score) {
        ProductVO productVO = new ProductVO();
        Product productInfo = this.getProductInfo(shopId, productId);
        GoodsError.CURRENT_GOODS_NOT_EXIST.trueThrow(productInfo == null);
        BeanUtil.copyProperties(productInfo, productVO);
        //赋值商品VO额外数据 商品展示分类
        productVO.setProductCategory(
                ISystem.shopId(shopId, () -> this.baseMapper.queryProductCategory(productInfo.getCategoryId()))
        );
        return productVO;
    }

    @Override
    public IPage<ProductVO> getProductInfoByNo(ProductParam productParam) {
        return TenantShop.disable(() -> baseMapper.queryProductList(productParam));
    }

    /**
     * 获取商品列表 by productParam
     *
     * @param productParam 查询条件
     * @return 商品列表信息
     */
    @Override
    public IPage<ProductVO> getProductList(ProductParam productParam) {
        IPage<ProductVO> result = baseMapper.queryProductList(productParam);
        List<ProductVO> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return result;
        }
        List<DistributeProductVO> distributeProductList = goodsAddonSupporter.getDistributeProductList(records.stream().map(ProductVO::getId).collect(Collectors.toSet()));
        Map<ActivityShopProductKey, ProductVO> shopProductKeyMap = records.stream()
                .collect(
                        Collectors.toMap(
                                record -> {
                                    ActivityShopProductKey key = new ActivityShopProductKey();

                                    key.setProductId(record.getId()).
                                            setShopId(record.getSellType() != SellType.CONSIGNMENT ? record.getShopId() : record.getSupplierId()).
                                            setActivityType(OrderType.COMMON).setActivityId(0L);
                                    return key;
                                },
                                v -> {
                                    Optional<DistributeProductVO> filterProduct = distributeProductList.stream().filter(eachProduct -> eachProduct.getProductId().equals(v.getId())).findFirst();
                                    if (filterProduct.isPresent()) {
                                        v.setDistributionStatus(filterProduct.get().getDistributionStatus());
                                        v.setOne(filterProduct.get().getOne());
                                    }
                                    return v;
                                }
                        )
                );
        Map<ActivityShopProductKey, List<StorageSku>> shopProductKeyListMap = storageRpcService.productSkuStockBatch(shopProductKeyMap.keySet());
        // 使用流来设置 StorageSkus 和调整价格
        shopProductKeyListMap.forEach(
                (shopProductKey, skuList) -> {
                    ProductVO productVO = shopProductKeyMap.get(shopProductKey);
                    productVO.setStorageSkus(skuList);
                    if (productVO.getSellType() == SellType.CONSIGNMENT) {
                        // 代销商品重新处理逻辑
                        handleConsignmentPrices(productVO, skuList);
                    }
                }
        );
        return result;
    }


    /**
     * 平台查询商品信息
     *
     * @param platformProductParam 查询条件
     * @return 平台商品信息
     */
    @Override
    public Page<PlatformProductVO> queryProductInfoByParam(PlatformProductParam platformProductParam) {
        Page<PlatformProductVO> result = baseMapper.queryProductInfoByParam(platformProductParam);
        List<PlatformProductVO> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return result;
        }

        Set<ActivityShopProductKey> skuKeys = new HashSet<>();
        records.forEach(bean -> {
            ActivityShopProductKey key = new ActivityShopProductKey();
            key.setProductId(bean.getId()).
                    setShopId(bean.getSellType() != SellType.CONSIGNMENT ? bean.getShopId() : bean.getSupplierId()).
                    setActivityType(OrderType.COMMON).setActivityId(0L);
            bean.setActivityShopProductKey(key);
            skuKeys.add(key);
        });

        Map<ActivityShopProductKey, List<StorageSku>> shopProductKeyListMap = storageRpcService.productSkuStockBatch(skuKeys);
        // 使用流来设置 StorageSkus 和调整价格
        records.forEach(bean -> {
                    List<StorageSku> storageSkus = shopProductKeyListMap.get(bean.getActivityShopProductKey());
                    log.warn(storageSkus.toString());
                    bean.setStorageSkus(storageSkus);
                    if (bean.getSellType() == SellType.CONSIGNMENT) {
                        // 代销商品重新处理逻辑
                        handleConsignmentPrices(bean, storageSkus);
                    }
                }
        );
        return result;
    }

    /**
     * 根据平台类目Id 获取商品信息List
     *
     * @param platformCategoryParam 商品查询param by平台类目
     * @param levelCategoryList     三级类目
     * @return Page<ApiPlatformProductVO>
     */
    @Override
    public Page<ApiPlatformProductVO> getProductInfoByPlatformCategoryId(List<Long> levelCategoryList, PlatformCategoryParam platformCategoryParam) {
        return baseMapper.queryProductInfoByPlatformCategoryIds(platformCategoryParam, levelCategoryList);
    }


    /**
     * 根据排序type获取店铺商品信息
     *
     * @param apiProductParam apiProductParam
     * @return 商品详情
     */
    @Override
    public Page<ApiProductVO> getProductInfoByParam(ApiProductParam apiProductParam) {
        return this.baseMapper.getProductInfoByParam(apiProductParam);
    }


    /**
     * 根据平台类目三级Id 获取商铺信息List
     *
     * @param platformCategoryParam platformCategoryParam
     * @return Page<ApiPlatformProductVO>
     */
    @Override
    public Page<ApiPlatformProductVO> getProductInfoByPlatformThirdlyCategoryId(PlatformCategoryParam platformCategoryParam) {
        Page<ApiPlatformProductVO> productPage = TenantShop.disable(() ->
                this.baseMapper.getProductInfoByPlatformThirdlyCategoryId(platformCategoryParam)
        );
        List<ApiPlatformProductVO> records = productPage.getRecords();
        if (records.isEmpty()) {
            return productPage;
        }
        Map<String, ProductStatisticsVO> productStatisticsMap = storageRpcService.getProductStatisticsMap(
                records.stream()
                        .map(
                                product -> new ShopProductKeyDTO()
                                        .setShopId(product.getShopId())
                                        .setProductId(product.getId())
                        )
                        .collect(Collectors.toList())
        );
        records.forEach(
                record -> record.setStatistics(
                        productStatisticsMap.get(
                                RedisUtil.key(record.getShopId(), record.getId())
                        )
                )
        );

        return productPage;
    }


    /**
     * 店铺状态改变 开启/禁用
     *
     * @param shopsEnableDisable 店铺禁用启用参数
     */
    @Override
    public void shopChange(ShopsEnableDisableDTO shopsEnableDisable) {
        Set<Long> shopIds = shopsEnableDisable.getShopIds();
        if (CollUtil.isEmpty(shopIds)) {
            return;
        }
        if (BooleanUtil.isTrue(shopsEnableDisable.getEnable())) {
            //店铺开启 上架店铺因禁用店铺下架的商品并设置缓存
            TenantShop.disable(() -> {
                this.lambdaUpdate()
                        .in(Product::getShopId, shopIds)
                        .eq(Product::getStatus, ProductStatus.UNUSABLE)
                        .set(Product::getStatus, ProductStatus.SELL_ON)
                        .update();
            });
            return;
        }
        // 店铺禁用 下架店铺正在上架的商品并删除缓存
        Set<String> productCacheKeys = shopIds.stream().flatMap((shopId) -> RedisUtil.keys(GoodsUtil.productCacheKeyPattern(shopId)).stream()).collect(Collectors.toSet());
        RedisUtil.doubleDeletion(
                () -> TenantShop.disable(
                        () -> {
                            LambdaUpdateChainWrapper<Product> updateWrapper = this.lambdaUpdate()
                                    .in(Product::getShopId, shopIds);
                            //如果是删除操作 则直接删除该店铺商品
                            if (shopsEnableDisable.getReason() == OperaReason.DELETED) {
                                updateWrapper.remove();
                                return;
                            }
                            updateWrapper.eq(Product::getStatus, ProductStatus.SELL_ON)
                                    .set(Product::getStatus, ProductStatus.UNUSABLE)
                                    .update();
                        }
                ),
                () -> {
                    if (CollUtil.isNotEmpty(productCacheKeys)) {
                        RedisUtil.delete(productCacheKeys);
                    }

                }
        );
    }


    /**
     * 获取商品信息
     *
     * @param shopId    店铺id
     * @param productId 商品id
     * @return 商品信息
     */
    @Override
    public Product getProductInfo(Long shopId, Long productId) {
        return init(() -> ISystem.shopId(shopId, () -> this.baseMapper.getProductInfoById(productId, Boolean.FALSE, shopId)), GoodsUtil.productCacheKey(shopId, productId));
    }

    /**
     * 批量获取商品 信息
     *
     * @param shopProductKeys shopId,productId
     * @return map<{ shopId, productId }, product>
     */
    @Override
    public Map<ShopProductKey, Product> getProductBatch(Set<ShopProductKey> shopProductKeys) {
        if (CollUtil.isEmpty(shopProductKeys)) {
            return Collections.emptyMap();
        }
        List<Product> productCache = this.getProductCache(shopProductKeys);
        Map<ShopProductKey, Product> productKeyMap = new HashMap<>(this.toProductKeyMap(productCache));
        if (productKeyMap.size() == shopProductKeys.size()) {
            return productKeyMap;
        }
        Set<ShopProductKey> needLoadKeys = shopProductKeys.stream().filter(key -> !productKeyMap.containsKey(key)).collect(Collectors.toSet());
        if (CollUtil.isEmpty(needLoadKeys)) {
            return productKeyMap;
        }
        productKeyMap.putAll(
                this.toProductKeyMap(TenantShop.disable(() -> baseMapper.getProductBatch(needLoadKeys)))
        );
        return productKeyMap;
    }

    @Override
    public List<Product> getProductListByShopId(Long shopId) {
        return baseMapper.getProductListByShopId(shopId);
    }

    /**
     * 获取商品信息
     *
     * @param productSupplier Supplier
     * @param key             redisKey
     * @return Product
     */
    @Override
    public Product init(Supplier<Product> productSupplier, String key) {
        return RedisUtil.getCacheMap(
                Product.class,
                productSupplier,
                Duration.ofSeconds(RedisUtil.expireWithRandom(CommonPool.UNIT_CONVERSION_TEN_THOUSAND)),
                Duration.ofMillis(CommonPool.NUMBER_FIFTEEN),
                key
        );
    }

    /**
     * 获取店铺在售商品数量
     *
     * @param shopId 店铺id
     * @return 店铺在售商品数量
     */
    @Override
    public Long getShopSalesProductCount(Long shopId) {
        return ISystem.shopId(
                shopId,
                () -> this.lambdaQuery()
                        .eq(Product::getStatus, ProductStatus.SELL_ON)
                        .count()
        );
    }

    /**
     * 获取平台三级类目下商品数量
     *
     * @param thirdIds 平台类目三级ids
     * @return map<平台类目ids, 商品数量>
     */
    @Override
    public List<ProductNumVo> getProductNumByPlatformThirdCategoryId(Set<Long> thirdIds) {
        return TenantShop.disable(
                () -> baseMapper.getProductNumByPlatformThirdCategoryId(thirdIds)
        );
    }


    /**
     * 获取随机商品
     *
     * @param productRandomParam 参数
     * @return 商品
     */
    @Override
    public Page<Product> randomGoods(ProductRandomParam productRandomParam) {
        return TenantShop.disable(() -> baseMapper.randomGoods(productRandomParam));
    }

    /**
     * 平台获取商品数量 by status
     *
     * @return List<商品状态数量VO>
     */
    @Override
    public Map<ProductStatus, Long> getGoodsQuantity() {
        HashMap<ProductStatus, Long> map = new HashMap<>(CommonPool.NUMBER_THREE);
        List<ProductStatusQuantityVO> productStatusQuantityList = TenantShop.disable(() -> baseMapper.queryGoodsQuantity());
        //获取平台下架商品数量
        Optional<Long> any = productStatusQuantityList.stream().filter(bean -> bean.getStatus() == ProductStatus.PLATFORM_SELL_OFF).map(ProductStatusQuantityVO::getQuantity).findAny();
        Long count = any.orElseGet(() -> Long.valueOf(CommonPool.NUMBER_ZERO));
        //获取商品数量
        Long reduce = productStatusQuantityList.stream()
                .filter(bean -> bean.getStatus() != ProductStatus.PLATFORM_SELL_OFF)
                .map(ProductStatusQuantityVO::getQuantity)
                .reduce(Long.valueOf(CommonPool.NUMBER_ZERO), Long::sum);
        map.put(ProductStatus.PLATFORM_SELL_OFF, count);

        // 获取供应商商品数量
        Map<String, Integer> supplierGoodsMap = goodsAddonSupporter.countProductsOfAllSupplier();
        Optional.ofNullable(supplierGoodsMap).map(item -> {
            map.put(ProductStatus.SELL_ON, reduce + item.get(GoodsConstant.ADDON_SUPPLIER_NEW_COUNT_PRODUCT_KEY));
            map.put(ProductStatus.PLATFORM_SELL_OFF, count + item.get(GoodsConstant.ADDON_SUPPLIER_IRREGULARITY_PRODUCT_KEY));
            return null;
        });
        if (ObjectUtil.isEmpty(supplierGoodsMap)) {
            map.put(ProductStatus.SELL_ON, reduce);
        }
        return map;
    }

    /**
     * 获取今日新增商品数量
     *
     * @return 今日新增商品数量
     */
    @Override
    public Long getTodayAddGoodsQuantity() {
        Final<Long> box = new Final<>(0L);
        ISecurity.match()
                .ifAnyShopAdmin(secureUser -> box.set(secureUser.getShopId()));
        Long count = TenantShop.disable(() -> baseMapper.queryTodayAddGoodsQuantity(box.get()));
        return count == null ? CommonPool.NUMBER_ZERO : count;
    }


    /**
     * 根据类目id 及类目级别 获取商品信息
     *
     * @param categoryRank 类目级别dto
     * @return Page<ApiProductVO>
     */
    @Override
    public Page<ApiProductVO> getProductInfoByCategoryId(CategoryRankDTO categoryRank) {
        if (categoryRank.getFilterProductStatus() == null) {
            categoryRank.setFilterProductStatus(true);
        }
        Set<Long> ids = categoryRank.getIds();
        switch (categoryRank.getCategoryLevel()) {
            case LEVEL_1:
                List<ProductCategory> oneCategory = productCategoryService.lambdaQuery().in(ProductCategory::getParentId, categoryRank.getIds()).list();
                if (CollUtil.isEmpty(oneCategory)) {
                    return null;
                }
                categoryRank.setIds(oneCategory.stream().map(BaseEntity::getId).collect(Collectors.toSet()));
            case LEVEL_2:
                List<ProductCategory> list = productCategoryService.lambdaQuery().in(ProductCategory::getParentId, categoryRank.getIds()).list();
                ids = list.stream().map(BaseEntity::getId).collect(Collectors.toSet());
                if (ids.size() < CommonPool.NUMBER_ONE) {
                    return null;
                }
                categoryRank.getIds().clear();
        }
        categoryRank.setIds(ids);
        return baseMapper.getProductInfoByCategoryId(categoryRank);
    }

    /**
     * pc端-看了又看
     *
     * @param productRandomParam 参数
     * @return 商品详情-看了又看
     */
    @Override
    public Page<ApiProductLookAndSeeVO> lookAndSeePage(ProductRandomParam productRandomParam) {
        Long shopId = ISystem.shopIdOpt().get();
        productRandomParam.setShopId(shopId);
        Page<Product> productPage = baseMapper.randomGoods(productRandomParam);
        List<Product> records = productPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return null;
        }
        Map<Long, Long> evaluatePerson = orderRpcService.getEvaluatePerson(
                records.stream().map(Product::getId).collect(Collectors.toSet()));
        List<ApiProductLookAndSeeVO> collect = records.stream().map(product -> {
            ApiProductLookAndSeeVO apiProductLookAndSeeVO = new ApiProductLookAndSeeVO();
            apiProductLookAndSeeVO.setProductId(product.getId())
                    .setProductName(product.getName())
                    .setProductPrice(product.getSalePrices().get(CommonPool.NUMBER_ZERO))
                    .setPic(product.getPic())
                    .setEvaluated(Option.of(evaluatePerson.get(product.getId())).getOrElse(Long.valueOf(CommonPool.NUMBER_ZERO)))
                    .setShopId(product.getShopId());
            return apiProductLookAndSeeVO;
        }).collect(Collectors.toList());
        return PageBean.getPage(productRandomParam, collect, productPage.getTotal(), productPage.getPages());
    }

    /**
     * pc端-店铺热销
     *
     * @param shopId 店铺id
     * @param size   查询数量
     * @return 店铺热销商品VO
     */
    @Override
    public List<ProductSaleVolumeVO> shopHotSales(Long shopId, Long size) {
        List<ProductSaleVolumeVO> shopProductSaleVolumes = storageRpcService.getShopProductSaleVolume(shopId, size);
        if (CollUtil.isEmpty(shopProductSaleVolumes)) {
            return null;
        }
        Map<Long, Long> evaluatePerson = orderRpcService.getEvaluatePerson(
                shopProductSaleVolumes.stream().map(ProductSaleVolumeVO::getProductId).collect(Collectors.toSet()));
        shopProductSaleVolumes.forEach(productSaleVolumeVO -> {
            Product product = Optional
                    .ofNullable(
                            /*TenantShop.disable(() -> this.lambdaQuery()
                            .select(Product::getName, Product::getPic)
                            .eq(Product::getId, productSaleVolumeVO.getProductId())
                            .one())*/
                            // 根据商品+店铺获取唯一的商品
                            this.lambdaQuery()
                                    .select(Product::getName, Product::getPic)
                                    .eq(Product::getId, productSaleVolumeVO.getProductId())
                                    .one()
                    ).orElse(new Product());

            productSaleVolumeVO.setProductName(product.getName())
                    .setPic(product.getPic())
                    .setEvaluated(Option.of(evaluatePerson.get(productSaleVolumeVO.getProductId())).getOrElse(Long.valueOf(CommonPool.NUMBER_ZERO)));
        });
        return shopProductSaleVolumes;
    }

    /**
     * pc端-热门关注
     *
     * @return 热门关注商品
     */
    @Override
    public List<ApiProductPopularAttentionVO> shopPopularAttention() {
        Long shopId = ISecurity.userMust().getShopId();
        // 根据shopId查询店铺商品的收藏
        List<Long> productIds = userRpcService.getShopProductCollection(shopId);
        IPage<Product> page = new Page<>(CommonPool.NUMBER_ONE, CommonPool.NUMBER_FIVE);
        page = this.lambdaQuery()
                .select(Product::getId, Product::getPic, Product::getName, Product::getSalePrices)
                .eq(Product::getStatus, ProductStatus.SELL_ON)
                .in(CollUtil.isNotEmpty(productIds), Product::getId, productIds)
                .page(page);
        List<Product> productList = page.getRecords();
        if (CollUtil.isEmpty(productList)) {
            return new ArrayList<>();
        }
        // 获取评价人数
        Map<Long, Long> evaluatePersonMap = orderRpcService.getEvaluatePerson(new HashSet<>(productList.stream().map(Product::getId).collect(Collectors.toSet())));
        return productList.stream().map(product -> {
            return new ApiProductPopularAttentionVO()
                    .setProductId(product.getId())
                    .setProductName(product.getName())
                    .setProductPrice(product.getSalePrices().get(CommonPool.NUMBER_ZERO))
                    .setPic(product.getPic())
                    .setEvaluated(evaluatePersonMap.get(product.getId()) == null ? 0L : evaluatePersonMap.get(product.getId()));
        }).collect(Collectors.toList());
    }

    /**
     * 根据平台三级类目ids 获取  ApiProductVO
     *
     * @param categoryRank 类目等级dto
     * @return ApiProductVO
     */
    @Override
    public Page<ApiProductVO> getApiProductInfoByPlatformCategoryId(CategoryRankDTO categoryRank) {
        return TenantShop.disable(() -> baseMapper.getApiProductInfoByPlatformCategoryId(categoryRank));
    }

    /**
     * 分页获取商品和规格信息
     *
     * @param productParam 商品查询参数
     * @return 商品规格信息
     */
    @Override
    public IPage<ProductSkusVO> getProductSkus(ProductParam productParam) {
        Page<Product> productPage = this.lambdaQuery()
                .select(Product::getId, Product::getName, Product::getPic, Product::getSalePrice, Product::getShopId)
                .eq(Product::getStatus, ProductStatus.SELL_ON)
                .eq(productParam.getCategoryId() != null, Product::getCategoryId, productParam.getCategoryId())
                .le(productParam.getLowestPrice() != null, Product::getSalePrice, productParam.getLowestPrice())
                .ge(productParam.getHighestPrice() != null, Product::getSalePrice, productParam.getHighestPrice())
                .like(productParam.getName() != null, Product::getName, productParam.getName())
                .page(new Page<>(productParam.getCurrent(), productParam.getSize()));
        List<Product> productList = productPage.getRecords();
        if (CollUtil.isEmpty(productList)) {
            return null;
        }
        List<ShopProductKeyDTO> shopProductKeys = productList.stream().map(product ->
                new ShopProductKeyDTO()
                        .setShopId(product.getShopId())
                        .setProductId(product.getId())
        ).collect(Collectors.toList());
        // 获取sku信息
        List<ProductSkusVO.SkuVO> skus = storageRpcService.getProductSkusByShopProductKeys(shopProductKeys);
        Map<Long, List<ProductSkusVO.SkuVO>> productSkuMap = skus.stream().collect(Collectors.groupingBy(ProductSkusVO.SkuVO::getProductId));
        List<ProductSkusVO> productSkuVOs = productList.stream().map(product -> {
            List<ProductSkusVO.SkuVO> skuVos = productSkuMap.get(product.getId());
            Long lowestPrice = skuVos != null ? skuVos.stream().min(Comparator.comparing(ProductSkusVO.SkuVO::getSkuPrice)).get().getSkuPrice() : 0L;
            Long highestPrice = skuVos != null ? skuVos.stream().max(Comparator.comparing(ProductSkusVO.SkuVO::getSkuPrice)).get().getSkuPrice() : 0L;
            ProductSkusVO productSkusVO = new ProductSkusVO();
            productSkusVO.setProductId(product.getId())
                    .setProductName(product.getName())
                    .setProductPic(product.getPic())
                    .setCategoryId(product.getCategoryId())
                    .setShopId(product.getShopId())
                    .setLowestPrice(lowestPrice)
                    .setHighestPrice(highestPrice)
                    .setSkus(skuVos);
            return productSkusVO;
        }).collect(Collectors.toList());
        return PageBean.getPage(productPage, productSkuVOs, productPage.getTotal(), productPage.getPages());
    }


    /**
     * 保存供应商商品评分
     *
     * @param orderCompleted 订单完成数据
     */
    @Override
    public void saveSupplierProductRate(OrderCompletedDTO orderCompleted) {
        List<OrderEvaluate> orderEvaluates = orderCompleted.getOrderEvaluates();
        if (CollUtil.isEmpty(orderEvaluates)) {
            return;
        }
        List<SupplierRateRecord> supplierRateRecordList = new ArrayList<>(orderEvaluates.size());
        orderEvaluates.forEach(bean -> {
            Product product = TenantShop.disable(() ->
                    this.lambdaQuery()
                            .eq(Product::getShopId, orderCompleted.getShopId())
                            .isNotNull(Product::getProviderId)
                            .eq(BaseEntity::getId, bean.getProductId()).one());
            if (product == null) {
                return;
            }
            SupplierRateRecord supplierRateRecord = new SupplierRateRecord();
            supplierRateRecord
                    .setProductId(bean.getId())
                    .setRate(bean.getRate())
                    .setShopId(bean.getShopId())
                    .setSupplierId(product.getProviderId());
            supplierRateRecordList.add(supplierRateRecord);
        });
        supplierRateRecordService.saveBatch(supplierRateRecordList);
    }

    /**
     * 获取条件商品信息 包含以删除商品信息
     *
     * @param shopId    店铺id
     * @param productId 商品id
     * @return 商品信息
     */
    @Override
    public Product getConditionProductInfo(Long shopId, Long productId) {
        return ISystem.shopId(shopId, () -> {
            Product productInfo = this.getProductInfo(shopId, productId);
            if (productInfo == null) {
                productInfo = this.baseMapper.getConditionProductInfo(shopId, productId);
            }
            return productInfo;
        });
    }

    @Override
    public List<ProductDeliveryVO> getProductDelivery(List<ShopProductSkuIdDTO> shopProductSkuIds) {
        Map<Long, List<Long>> shopProductIdMap = shopProductSkuIds.stream()
                .collect(Collectors.groupingBy(ShopProductSkuIdDTO::getShopId,
                        Collectors.mapping(
                                ShopProductSkuIdDTO::getProductId,
                                Collectors.toList()
                        )));
        Set<Long> productIds = shopProductIdMap.values()
                .stream().flatMap(Collection::stream)
                .collect(Collectors.toSet());
        List<Product> products = TenantShop.disable(() -> this.lambdaQuery()
                .select(Product::getId, Product::getShopId, Product::getFreightTemplateId, Product::getDistributionMode)
                .in(Product::getId, productIds)
                .in(Product::getShopId, shopProductIdMap.keySet())
                .eq(Product::getStatus, ProductStatus.SELL_ON)
                .list()
        );
        if (CollectionUtils.isEmpty(products)) {
            return Collections.emptyList();
        }
        Map<ActivityShopProductKey, List<StorageSku>> activityShopProductKeyListMap = storageRpcService.productSkuStockBatch(
                products.stream()
                        .map(product -> {
                            ActivityShopProductKey activityShopProductKey = new ActivityShopProductKey();
                            activityShopProductKey.setProductId(product.getId());
                            activityShopProductKey.setShopId(product.getShopId());
                            activityShopProductKey.setActivityId(0L);
                            activityShopProductKey.setActivityType(OrderType.COMMON);
                            return activityShopProductKey;
                        }).collect(Collectors.toSet())
        );
        Map<Long, Product> productMap = products.stream()
                .collect(Collectors.toMap(Product::getId, v -> v));
        Map<Long, BigDecimal> skuWeightMap = new HashMap<>(shopProductSkuIds.size());
        if (CollUtil.isNotEmpty(activityShopProductKeyListMap)) {
            skuWeightMap = activityShopProductKeyListMap.values()
                    .stream().flatMap(Collection::stream)
                    .toList().stream()
                    .collect(Collectors.toMap(StorageSku::getId, StorageSku::getWeight));
        }
        Map<Long, BigDecimal> finalSkuWeightMap = skuWeightMap;
        return shopProductSkuIds.stream()
                .filter(shopProduct -> productMap.get(shopProduct.getProductId()) != null)
                .map(shopProductSkuId -> {
                    Long productId = shopProductSkuId.getProductId();
                    Long skuId = shopProductSkuId.getSkuId();
                    Product product = productMap.get(productId);
                    return new ProductDeliveryVO()
                            .setShopId(shopProductSkuId.getShopId())
                            .setProductId(productId)
                            .setDistributionMode(product.getDistributionMode())
                            .setFreightTemplateId(product.getFreightTemplateId())
                            .setSkuId(skuId)
                            .setWeight(finalSkuWeightMap.get(skuId));
                }).toList();
    }

    /**
     * 查询当前签约类目商品是否存在
     *
     * @param signingCategorySecondIds 二级签约类目ids
     * @param shopId                   店铺id
     * @return 是否存在
     */
    @Override
    public boolean getSigningCategoryProduct(Set<Long> signingCategorySecondIds, Long shopId) {
        return this.baseMapper.querySigningCategoryProduct(signingCategorySecondIds, shopId);
    }

    /**
     * 商品名称修改
     *
     * @param id   商品id
     * @param name 商品名称
     */
    @Override
    public void updateProductName(Long id, String name) {
        Long shopId = ISecurity.userMust().getShopId();
        Product product = checkoutProductInfo(id, shopId, Boolean.FALSE);
        product.setName(name);
        // 更新mysql redis
        updateProduct2DB(product, product.getExtra());
    }

    /**
     * 获取商品库存基础信息
     *
     * @param param 检索条件
     * @return IPage<ProductStockVO>
     */

    @Override
    public IPage<ProductStockVO> getProductStockBaseInfo(ProductStockParam param) {
        IPage<ProductStockVO> result = this.baseMapper.queryProductStockBaseInfo(param);
        List<ProductStockVO> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return result;
        }
        Map<ActivityShopProductKey, ProductStockVO> shopProductKeyMap = records.stream()
                .collect(
                        Collectors.toMap(
                                record -> {
                                    ActivityShopProductKey key = new ActivityShopProductKey();
                                    key.setProductId(record.getId()).setShopId(record.getShopId()).setActivityType(OrderType.COMMON).setActivityId(0L);
                                    return key;
                                },
                                v -> v
                        )
                );
        Map<ActivityShopProductKey, List<StorageSku>> shopProductKeyListMap = storageRpcService.productSkuStockBatch(shopProductKeyMap.keySet());
        shopProductKeyMap.forEach(
                (shopProductKey, productStockVO) -> productStockVO.setStorageSkus(shopProductKeyListMap.get(shopProductKey))
        );
        return result;
    }

    /**
     * 获取采购发布商品VO信息
     *
     * @param param 查询param
     * @return IPage<SupplierIssueProductListVO>
     */
    @Override
    public IPage<SupplierIssueProductListVO> getPurchaseIssueProducts(PurchaseProductParam param) {
        // 全部 跟下架 查询删除的商品
        if (param.getStatus() == null || param.getStatus() == ProductStatus.SELL_OFF) {
            param.setIsDeleted(Boolean.TRUE);
        }

        IPage<SupplierIssueProductListVO> page = this.baseMapper.queryPurchaseIssueProducts(param);
        List<SupplierIssueProductListVO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return page;
        }
        supplierGoods(records);
        return page;
    }


    /**
     * 供应商商品状态更新
     *
     * @param supplierGoodsUpdateStatus 更新的商品数据
     */
    @Override
    public void supplierGoodsUpdateStatus(SupplierGoodsUpdateStatusDTO supplierGoodsUpdateStatus) {
        ProductStatus productStatus = supplierGoodsUpdateStatus.getProductUpdateStatus().get(0).getProductStatus();
        List<ProductUpdateStatusDTO> updateStatusList = supplierGoodsUpdateStatus.getProductUpdateStatus()
                .stream()
                .map(item -> {
                            List<Product> productList = new ArrayList<>();
                            List<ProductStatus> productStatuses = switch (productStatus) {
                                case SELL_ON -> Collections.singletonList(ProductStatus.SUPPLIER_SELL_OFF);
                                case SELL_OFF -> Arrays.asList(ProductStatus.SELL_ON, ProductStatus.SELL_OFF);
                                case PLATFORM_SELL_OFF -> Arrays.asList(ProductStatus.SELL_OFF, ProductStatus.SELL_ON, ProductStatus.SUPPLIER_SELL_OFF, ProductStatus.SUPPLIER_DISABLE);
                                default -> Collections.emptyList();
                            };
                            if (CollUtil.isNotEmpty(productStatuses)) {
                                productList = TenantShop.disable(
                                        () -> lambdaQuery()
                                                .select(Product::getId, Product::getShopId, Product::getStatus)
                                                .in(Product::getId, item.getProductIds())
                                                .in(Product::getStatus, productStatuses)
                                                .eq(Product::getSellType, SellType.CONSIGNMENT)
                                                .eq(Product::getSupplierId, item.getShopId())
                                                .list()
                                );
                            }
                            return productList;
                        }
                ).filter(CollUtil::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(Product::getShopId))
                .entrySet()
                .stream().map(entry -> new ProductUpdateStatusDTO()
                        .setShopId(entry.getKey())
                        .setProductIds(entry.getValue().stream().map(Product::getId).collect(Collectors.toSet()))
                        .setProductStatus(productStatus == ProductStatus.SELL_ON ? ProductStatus.SELL_OFF : productStatus)
                ).collect(Collectors.toList());
        handlerGoodsStatus(productStatus == ProductStatus.SELL_OFF ? ProductStatus.SUPPLIER_SELL_OFF : productStatus == ProductStatus.SELL_ON ? ProductStatus.SELL_OFF : productStatus, supplierGoodsUpdateStatus.getProductViolation(), updateStatusList);
    }


    @Override
    public void supplierForceGoodsStatus(Set<ShopProductKey> keys) {
        if (CollUtil.isEmpty(keys)) {
            return;
        }
        boolean hashProductId = keys.iterator().next().getProductId() != null;
        Set<ShopProductKey> shopProductKeys = baseMapper.queryGoodsByProductIds(hashProductId, keys);
        if (CollUtil.isEmpty(shopProductKeys)) {
            return;
        }
        Set<String> cacheKeys = shopProductKeys.stream().map(shopProductKey -> GoodsUtil.productCacheKey(shopProductKey.getShopId(), shopProductKey.getProductId())).collect(Collectors.toSet());
        RedisUtil.doubleDeletion(
                () -> baseMapper.supplierForceGoodsStatus(shopProductKeys),
                () -> RedisUtil.delete(cacheKeys)
        );
        Map<Long, List<ShopProductKey>> collect = shopProductKeys.stream().collect(Collectors.groupingBy(ShopProductKey::getShopId));
        collect.forEach((key, value) ->
                goodsExecutor.execute(
                        () -> rabbitTemplate.convertAndSend(
                                GoodsRabbit.GOODS_DELETE.exchange(),
                                GoodsRabbit.GOODS_DELETE.routingKey(),
                                new ProductDeleteDTO()
                                        .setShopId(key)
                                        .setProductIds(value.stream().map(ShopProductKey::getProductId).collect(Collectors.toSet()))
                        )
                ));
    }

    /**
     * 供应商商品信息修改
     *
     * @param supplierProduct 商品信息
     */
    @Override
    public void supplierUpdateGoods(Product supplierProduct) {
        List<Product> products = TenantShop.disable(
                () -> lambdaQuery()
                        .eq(Product::getSupplierId, supplierProduct.getShopId())
                        .eq(Product::getSellType, SellType.CONSIGNMENT)
                        .eq(Product::getId, supplierProduct.getId())
                        .list()
        );
        if (CollUtil.isEmpty(products)) {
            return;
        }
        products.forEach(product -> {
            handlerSupplierGoods(product, supplierProduct);
            Integer update = TenantShop.disable(
                    () -> RedisUtil.doubleDeletion(
                            () -> baseMapper.update(product, Wrappers.lambdaQuery(Product.class)
                                    .eq(Product::getId, product.getId())
                                    .eq(Product::getShopId, product.getShopId())),
                            RedisUtil.key(GoodsConstant.GOODS_DETAIL_CACHE_KEY, product.getShopId(), product.getId()
                            ))
            );
            GoodsError.PRODUCT_UPDATE_FAIL.trueThrow(update < CommonPool.NUMBER_ONE);
            sendProductBroadcast(product.getExtra().getShopCategory(), product.getExtra().getPlatformCategory(), product, GoodsRabbit.GOODS_UPDATE.exchange(), GoodsRabbit.GOODS_UPDATE.routingKey());
        });

    }

    /**
     * 获取代销商品信息
     *
     * @param id 商品id
     */
    @Override
    public ProductVO getConsignmentProductInfo(Long id) {
        ProductVO productVO = new ProductVO();
        Product product = this.lambdaQuery().select(
                        Product::getName,
                        Product::getExtra,
                        BaseEntity::getId,
                        Product::getShopId,
                        Product::getSupplierId,
                        Product::getProductType,
                        Product::getSellType,
                        Product::getWidePic,
                        Product::getVideoUrl,
                        Product::getSaleDescribe,
                        Product::getStatus,
                        Product::getSalePrices
                )
                .eq(BaseEntity::getId, id)
                .eq(Product::getShopId, ISystem.shopIdOpt().get())
                .ne(Product::getStatus, ProductStatus.SUPPLIER_SELL_OFF)
                .one();
        GoodsError.CURRENT_CONSIGNMENT_PRODUCT_NOT_EXIST_OR_EXCEPTION.trueThrow(product == null);
        BeanUtil.copyProperties(product, productVO);
        Set<ShopProductKey> shopProductKeys = new HashSet<>();
        shopProductKeys.add(new ShopProductKey().setProductId(product.getId()).setShopId(product.getSupplierId()));
        List<StorageSpecSkuDTO> storageSpecSku = shopProductKeys.stream()
                .map(key -> storageRpcService.getStorageSpecSku(Collections.singleton(key)))
                .findFirst()
                .orElse(Collections.emptyList());
        productVO.setStorageSpecSku(storageSpecSku);
        return productVO;
    }

    /**
     * 代销商品修改
     *
     * @param consignmentProduct 代销商品修改DTO
     */
    @Override
    public void consignmentProductUpdate(ConsignmentProductDTO consignmentProduct) {
        Long shopId = ISecurity.userMust().getShopId();
        consignmentProduct.getConsignmentPriceSetting().validParam();
        Product product = checkoutProductInfo(consignmentProduct.getId(), shopId, Boolean.FALSE);
        if (product.getSellType() != SellType.CONSIGNMENT && product.getStatus() == ProductStatus.SUPPLIER_DISABLE) {
            throw new GlobalException("商品异常");
        }
        List<StorageSpecSkuDTO> storageSpecSku = storageRpcService.getStorageSpecSku(Collections.singleton(new ShopProductKey()
                .setProductId(product.getId())
                .setShopId(product.getSupplierId())
        ));
        List<Long> sortedSalePrices = storageSpecSku.get(CommonPool.NUMBER_ZERO).getSkus().stream().map(SkuDTO::getSalePrice).toList();
        ConsignmentPriceSettingDTO consignmentPriceSetting = consignmentProduct.getConsignmentPriceSetting();
        //代销商品修改商品价格
        boolean isRegular = consignmentPriceSetting.getType() == PricingType.REGULAR;
        sortedSalePrices = sortedSalePrices.stream()
                .map(sortedSalePrice -> sortedSalePrice + (isRegular
                        ? consignmentPriceSetting.getSale()
                        : (sortedSalePrice * consignmentPriceSetting.getSale() / 1000000))).toList();
        //代销商品修改商品价格
        ProductExtraDTO extra = product.getExtra();
        product.setName(consignmentProduct.getName());
        product.setSalePrice(sortedSalePrices.get(CommonPool.NUMBER_ZERO));
        product.setSalePrices(sortedSalePrices);
        product.setExtra(extra
                .setConsignmentPriceSetting(consignmentProduct.getConsignmentPriceSetting())
                .setShopCategory(consignmentProduct.getShopCategory()));
        product.setSaleDescribe(consignmentProduct.getSaleDescribe());

        updateProduct2DB(product, extra);
    }

    /**
     * 商品复制
     *
     * @param product         商品
     * @param supplierProduct 修改商品的信息
     */
    private void handlerSupplierGoods(Product product, Product supplierProduct) {
        ConsignmentPriceSettingDTO consignmentPriceSetting = product.getExtra().getConsignmentPriceSetting();
        //代销商品修改商品价格
        boolean isRegular = consignmentPriceSetting.getType() == PricingType.REGULAR;
        List<Long> salePrices = supplierProduct.getSalePrices().stream()
                .map(sortedSalePrice -> isRegular
                        ? consignmentPriceSetting.getSale()
                        : (sortedSalePrice * consignmentPriceSetting.getSale() / 1000000)).toList();
        product.setAlbumPics(supplierProduct.getAlbumPics())
                .setDetail(supplierProduct.getDetail())
                .setPic(supplierProduct.getPic())
                .setSalePrices(salePrices)
                .setSalePrice(salePrices.get(0))
                .setVideoUrl(supplierProduct.getVideoUrl())
                .setBrandId(supplierProduct.getBrandId())
                .setWidePic(supplierProduct.getWidePic())
                .setFreightTemplateId(supplierProduct.getFreightTemplateId())
                .setExtra(product.getExtra()
                        .setSupplierCustomDeductionRatio(supplierProduct.getExtra().getCustomDeductionRatio())
                        .setProductParameters(supplierProduct.getExtra().getProductParameters())
                        .setProductAttributes(supplierProduct.getExtra().getProductAttributes()))
        ;
    }

    /**
     * 获取缓存中的商品
     *
     * @param shopProductKeys ShopProductKey
     * @return List<Product>
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private List<Product> getProductCache(Set<ShopProductKey> shopProductKeys) {
        //获取缓存中的商品
        List<Object> caches = RedisUtil.executePipelined(
                redisOperations -> {
                    HashOperations hashOperations = redisOperations.opsForHash();
                    for (ShopProductKey shopProductKey : shopProductKeys) {
                        hashOperations.entries(GoodsUtil.productCacheKey(shopProductKey.getShopId(), shopProductKey.getProductId()));
                    }
                }
        );
        return FastJson2.convert(
                caches.stream().filter(cache -> {
                    if (!(cache instanceof Map map)) {
                        return false;
                    }
                    return !map.isEmpty();
                }).toList(), new TypeReference<>() {
                });
    }

    /**
     * 获取已发布的供应商商品  采购 正常返回   代销要计算相关金额数据
     *
     * @param records 供应商商品信息
     */
    private void supplierGoods(List<SupplierIssueProductListVO> records) {
        if (records.size() > 0) {
            Set<Long> supplierIds = records.stream().map(SupplierIssueProductListVO::getSupplierId).collect(Collectors.toSet());
            List<ShopInfoVO> shopInfoByShopIdList = shopRpcService.getShopInfoByShopIdList(supplierIds);
            Map<Long, String> shopInfoMap = shopInfoByShopIdList.stream().collect(Collectors.toMap(ShopInfoVO::getId, ShopInfoVO::getName));
            records.forEach(bean -> {
                Long supplierId = bean.getSupplierId();
                String supplierName = shopInfoMap.get(supplierId);
                bean.setSupplierName(supplierName);
            });
        }
        Map<ActivityShopProductKey, SupplierIssueProductListVO> shopProductKeyMap = records.stream()
                .collect(
                        Collectors.toMap(
                                record -> {
                                    ActivityShopProductKey key = new ActivityShopProductKey();
                                    key.setProductId(record.getId())
                                            .setShopId(record.getSellType() == SellType.CONSIGNMENT ? record.getSupplierId() : record.getShopId())
                                            .setActivityType(OrderType.COMMON)
                                            .setActivityId(0L);
                                    return key;
                                },
                                v -> v
                        )
                );
        Map<ActivityShopProductKey, List<StorageSku>> shopProductKeyListMap = storageRpcService.productSkuStockBatch(shopProductKeyMap.keySet());

        // 使用流来设置 StorageSkus 和调整价格
        shopProductKeyListMap.forEach(
                (shopProductKey, skuList) -> {
                    SupplierIssueProductListVO supplierIssueProductListVO = shopProductKeyMap.get(shopProductKey);
                    supplierIssueProductListVO.setStorageSkus(skuList);
                    if (supplierIssueProductListVO.getSellType() == SellType.CONSIGNMENT) {
                        // 代销商品重新处理逻辑
                        handleConsignmentPrices(supplierIssueProductListVO, skuList);
                    }
                }
        );

    }

    /**
     * 采购发布商品修改状态
     *
     * @param id 商品id
     */
    @Override
    public void purchaseIssueProductUpdateStatus(Long id) {
        Long shopId = ISystem.shopIdOpt().get();
        Product productInfo = this.baseMapper.getProductInfoById(id, Boolean.TRUE, shopId);
        GoodsError.CURRENT_GOODS_NOT_EXIST.trueThrow(productInfo == null);
        assert productInfo != null;
        GoodsError.CURRENT_PRODUCT_NOT_AVAILABLE.trueThrow(productInfo.getSellType() != SellType.PURCHASE || productInfo.getStatus() != ProductStatus.SELL_OFF);
        supplierSellOnGoodsHandler(productInfo);

    }


    /**
     * 采购商品处理
     *
     * @param productInfo 商品信息
     */
    private void supplierSellOnGoodsHandler(Product productInfo) {
        Boolean deleted = productInfo.getDeleted();

        this.baseMapper.updateSupplierSellGoods(productInfo.getId(), productInfo.getShopId());

        /*  更新es
         */
        if (deleted) {
            // 确保更新商品状态为未删除状态
            productInfo.setDeleted(Boolean.FALSE);
            productInfo.setStatus(ProductStatus.SELL_ON);
            goodsExecutor.execute(
                    () -> rabbitTemplate.convertAndSend(
                            GoodsRabbit.GOODS_RELEASE.exchange(),
                            GoodsRabbit.GOODS_RELEASE.routingKey(),
                            new ProductBroadcastDTO().setProduct(productInfo)
                                    .setShopCategory(productInfo.getExtra().getShopCategory())
                                    .setPlatformCategory(productInfo.getExtra().getPlatformCategory())
                    )
            );
            return;
        }
        ProductUpdateStatusDTO updateStatus = new ProductUpdateStatusDTO();
        updateStatus.setShopId(productInfo.getShopId())
                .setProductStatus(ProductStatus.SELL_ON)
                .setProductIds(Collections.singleton(productInfo.getId()));
        List<ProductUpdateStatusDTO> updateStatusList = Collections.singletonList(updateStatus);
        goodsExecutor.execute(
                () -> rabbitTemplate.convertAndSend(
                        GoodsRabbit.GOODS_UPDATE_STATUS.exchange(),
                        GoodsRabbit.GOODS_UPDATE_STATUS.routingKey(),
                        updateStatusList
                )
        );
    }


    /**
     * 已铺货代销商品
     *
     * @param purchaseProductParam 查询参数
     */
    @Override
    public IPage<SupplierIssueProductListVO> getPaveGoods(PurchaseProductParam purchaseProductParam) {
        IPage<SupplierIssueProductListVO> page = baseMapper.getPaveGoods(purchaseProductParam);
        List<SupplierIssueProductListVO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return page;
        }
        supplierGoods(records);
        return page;
    }

    /**
     * 代销商品修改状态
     *
     * @param productId 商品id
     */
    @Override
    public void consignmentProductUpdateStatus(Long productId) {
        Long shopId = ISystem.shopIdOpt().get();
        Product productInfo = this.baseMapper.getProductInfoById(productId, Boolean.TRUE, shopId);
        if (productInfo == null) {
            throw new GlobalException("当前商品不存在");
        }
        if (!validConsignmentProductStatus(productInfo.getStatus())) {
            throw new GlobalException("当前商品不可进行上架操作");
        }
        supplierSellOnGoodsHandler(productInfo);
    }

    /**
     * 根据{@code supplierId}和{@code productId}获取商品信息
     *
     * @param supplierId 供应商ID
     * @param productId  商品id
     * @return {@link Product}
     */
    @Override
    public Product getProductBySupplierIdAndProductId(Long supplierId, Long productId) {
        List<Product> products = this.baseMapper.getProductBySupplierIdAndProductId(supplierId, productId, Boolean.TRUE);
        if (CollectionUtils.isEmpty(products)) {
            return null;
        }
        return products.get(0);
    }

    @Override
    public IPage<ProductRenderVO> queryProductRenderVO(ProductRenderParam productRenderParam) {
        return this.baseMapper.queryProductRenderVO(productRenderParam);
    }

    @Override
    public List<ProductVO> getProductByName(YihuProductQueryDTO yihuProductQueryDTO) {
        List<ProductVO> productVOList = TenantShop.disable(() -> this.baseMapper.getProductByName(yihuProductQueryDTO));

        if (CollUtil.isEmpty(productVOList)) {
            return new ArrayList<>();
        }
        Map<ActivityShopProductKey, ProductVO> shopProductKeyMap = productVOList.stream()
                .collect(
                        Collectors.toMap(
                                record -> {
                                    ActivityShopProductKey key = new ActivityShopProductKey();

                                    key.setProductId(record.getId()).
                                            setShopId(record.getSellType() != SellType.CONSIGNMENT ? record.getShopId() : record.getSupplierId()).
                                            setActivityType(OrderType.COMMON).setActivityId(0L);
                                    return key;
                                },
                                v -> v
                        )
                );
        Map<ActivityShopProductKey, List<StorageSku>> shopProductKeyListMap = storageRpcService.productSkuStockBatch(shopProductKeyMap.keySet());
        // 使用流来设置 StorageSkus 和调整价格
        shopProductKeyListMap.forEach(
                (shopProductKey, skuList) ->

                {
                    ProductVO productVO = shopProductKeyMap.get(shopProductKey);
                    productVO.setStorageSkus(skuList);
                    if (productVO.getSellType() == SellType.CONSIGNMENT) {
                        // 代销商品重新处理逻辑
                        handleConsignmentPrices(productVO, skuList);
                    }
                }
        );
        return productVOList;
    }

    /**
     * 判断代销商品状态是否可以上架
     *
     * @return Boolean
     */
    private Boolean validConsignmentProductStatus(ProductStatus productStatus) {
        switch (productStatus) {
            case SUPPLIER_SELL_OFF, SUPPLIER_DISABLE, PLATFORM_SELL_OFF -> {
                return Boolean.FALSE;
            }
            default -> {
                return Boolean.TRUE;
            }
        }

    }

    /**
     * toProductKeyMap
     *
     * @param products 商品List
     * @return Map<ShopProductKey, Product>
     */
    private Map<ShopProductKey, Product> toProductKeyMap(List<Product> products) {
        if (CollUtil.isEmpty(products)) {
            return Collections.emptyMap();
        }
        return products.stream().collect(Collectors.toMap(
                product -> new ShopProductKey().setShopId(product.getShopId()).setProductId(product.getId()),
                product -> product
        ));
    }


    /**
     * 商品信息校验
     *
     * @param id                      商品id
     * @param shopId                  店铺id
     * @param isNotFiltrationSellType 是否过滤类型查询
     * @return Product
     */
    private Product checkoutProductInfo(Long id, Long shopId, boolean isNotFiltrationSellType) {
        Product product = this.lambdaQuery()
                .eq(BaseEntity::getId, id)
                .eq(Product::getShopId, shopId)
                .ne(isNotFiltrationSellType, Product::getSellType, SellType.CONSIGNMENT)
                .one();
        GoodsError.CURRENT_GOODS_NOT_EXIST.trueThrow(product == null);
        assert product != null;
        GoodsError.PRODUCT_STATUS_OPERATE_EXCEPTION.trueThrow(product.getStatus() == ProductStatus.PLATFORM_SELL_OFF);
        return product;
    }


    /**
     * 商品扩展字段处理
     *
     * @param product                 商品id
     * @param platformCategory        平台类目信息
     * @param shopCategory            店铺类目信息
     * @param productAttributes       产品属性
     * @param productParameters       产品参数
     * @param consignmentPriceSetting 代销价格设置
     */
    private void setExtendInfo(Product product, com.medusa.gruul.goods.api.model.CategoryLevel platformCategory, com.medusa.gruul.goods.api.model.CategoryLevel shopCategory, List<ProductFeaturesValueDTO> productAttributes, List<ProductFeaturesValueDTO> productParameters, ConsignmentPriceSettingDTO consignmentPriceSetting) {
        Optional.ofNullable(productAttributes)
                .ifPresent(params -> {
                    List<ProductFeaturesValueDTO> collect = params.stream()
                            .collect(Collectors.toMap(ProductFeaturesValueDTO::getId, entity -> entity, (existing, replacement) -> existing))
                            .values()
                            .stream()
                            .toList();
                    GoodsError.ATTRIBUTES_REPETITION.trueThrow(params.size() != collect.size());
                    params.forEach(productAttribute -> productAttribute.getFeatureValues().forEach(featureValue -> {
                        if (featureValue.getFeatureValueId() == null) {
                            featureValue.setFeatureValueId(MybatisPlusConfig.IDENTIFIER_GENERATOR.nextId(new FeatureValueDTO()).longValue());
                        }
                    }));
                });
        // 获取自定义扣率
        Long customDeductionRatio = goodsAddonSupporter.getCustomDeductionRatio(platformCategory.getTwo(), product.getShopId());


        ProductExtraDTO productExtra = new ProductExtraDTO();
        // 设置自定义扣率,商品类目,平台类目,产品属性 ，产品参数
        productExtra.setCustomDeductionRatio(customDeductionRatio == null ? 0L : customDeductionRatio);
        productExtra.setShopCategory(shopCategory);
        productExtra.setPlatformCategory(platformCategory);
        productExtra.setProductParameters(productParameters);
        productExtra.setProductAttributes(productAttributes);
        if (BeanUtil.isNotEmpty(consignmentPriceSetting)) {
            consignmentPriceSetting.validParam();
            productExtra.setConsignmentPriceSetting(consignmentPriceSetting);
        }
        product.setExtra(productExtra);
    }


    /**
     * 商品状态处理
     *
     * @param status           改变的状态
     * @param productViolation 商品违规信息
     * @param updateStatusList List<ProductUpdateStatusDTO>
     */
    private void handlerGoodsStatus(ProductStatus status, ProductViolationDTO productViolation, List<ProductUpdateStatusDTO> updateStatusList) {
        updateStatusList.forEach(
                updateStatus -> {
                    if (CollUtil.isEmpty(updateStatus.getProductIds())) {
                        return;
                    }
                    Set<String> keys = updateStatus.getProductIds()
                            .stream()
                            .map(productId -> GoodsUtil.productCacheKey(updateStatus.getShopId(), productId))
                            .collect(Collectors.toSet());
                    RedisUtil.doubleDeletion(
                            () -> {
                                TenantShop.disable(
                                        () -> {
                                            LambdaUpdateChainWrapper<Product> set = this.lambdaUpdate()
                                                    .in(Product::getId, updateStatus.getProductIds())
                                                    .eq(Product::getShopId, updateStatus.getShopId())
                                                    .set(Product::getStatus, status);
                                            if (productViolation != null) {
                                                set.setSql(
                                                        SqlHelper.renderJsonSetSql("extra", Tuple.of("productViolation", JSON.toJSONString(productViolation.setExamineDateTime(LocalDateTime.now())), null))
                                                );

                                            }
                                            set.update();
                                        });
                            },
                            () -> RedisUtil.delete(keys)
                    );

                }
        );
        goodsExecutor.execute(
                () -> {
                    rabbitTemplate.convertAndSend(
                            GoodsRabbit.GOODS_UPDATE_STATUS.exchange(),
                            GoodsRabbit.GOODS_UPDATE_STATUS.routingKey(),
                            updateStatusList
                    );
                    if (status == ProductStatus.SELL_OFF || status == ProductStatus.PLATFORM_SELL_OFF) {
                        goodsExecutor.execute(
                                () -> rabbitTemplate.convertAndSend(
                                        GoodsRabbit.GOODS_SELL_OFF.exchange(),
                                        GoodsRabbit.GOODS_SELL_OFF.routingKey(),
                                        updateStatusList
                                )
                        );
                    }
                }
        );
    }

    /**
     * 发送mq广播  发布 修改
     *
     * @param shopCategory     店铺类目信息
     * @param platformCategory 平台类目信息
     * @param product          商品信息
     * @param exchange         exchange
     * @param routingKey       routingKey
     */
    private void sendProductBroadcast(CategoryLevel shopCategory, CategoryLevel platformCategory, Product product, String exchange, String routingKey) {
        goodsExecutor.execute(() -> rabbitTemplate.convertAndSend(
                exchange,
                routingKey,
                new ProductBroadcastDTO().setProduct(product)
                        .setShopCategory(shopCategory)
                        .setPlatformCategory(platformCategory)
        ));
    }


    /**
     * 编辑商品Sku信息
     *
     * @param productDto 商品DTO
     * @param product    商品信息
     * @param skus       skus信息
     */
    private void editProductSkuInfo(ProductDTO productDto, Product product, List<SkuDTO> skus) {
        // 编辑(新增/修改) 商品sku信息
        if (productDto.getSellType() != SellType.CONSIGNMENT) {
            StorageSpecSkuDTO storageSpecSkuDTO = new StorageSpecSkuDTO();
            skus.stream()
                    .filter(sku -> StrUtil.isBlank(sku.getImage()))
                    .forEach(sku -> sku.setImage(product.getPic()));
            storageSpecSkuDTO.setProductId(product.getId());
            storageSpecSkuDTO.setSkus(skus);
            storageSpecSkuDTO.setSpecGroups(productDto.getSpecGroups());
            storageSpecSkuDTO.setProductName(productDto.getName());
            storageSpecSkuDTO.setSellType(productDto.getSellType());
            storageRpcService.saveOrUpdateSpecSku(storageSpecSkuDTO);
        }
    }

    /**
     * 编辑商品供应商Sku价格信息
     *
     * @param productDto 商品DTO
     * @param product    商品信息
     * @param skus       skus信息
     */
    private void editSupplierSkuInfo(ProductDTO productDto, Product product, List<SkuDTO> skus) {
        // 编辑(新增/修改) 商品sku信息
        if (null != productDto.getProviderId()) {
            SupplierGoodsSpecSkuDTO supplierGoodsSpecSkuDTO = new SupplierGoodsSpecSkuDTO();
            supplierGoodsSpecSkuDTO.setProductId(product.getId());
            List<SupplierProductSku> supplierProductSkus = Lists.newArrayList();
            List<SupplierGoodsSkuDTO> supplierGoodsSkus = Lists.newArrayList();
            skus.forEach(sku -> {
                //价格关联
                SupplierGoodsSkuDTO dto = new SupplierGoodsSkuDTO();
                dto.setPrice(sku.getSupplierPrice());
                dto.setSupplierId(productDto.getProviderId());
                dto.setSkuId(sku.getId());
                dto.setShopId(product.getShopId());
                dto.setProductId(product.getId());
                supplierGoodsSkus.add(dto);
                //库存关联
                if (null != sku.getSupplierProductId()) {
                    SupplierProductSku supplierProductSku = new SupplierProductSku();
                    supplierProductSku.setStorageSkuId(sku.getId())
                            .setProductId(product.getId())
                            .setSupplierId(productDto.getProviderId())
                            .setSupplierProductId(sku.getSupplierProductId());
                    supplierProductSkus.add(supplierProductSku);
                }
            });
            supplierGoodsSpecSkuDTO.setSupplierGoodsSkus(supplierGoodsSkus);
            goodsRpcService.saveOrUpdateSupperSku(supplierGoodsSpecSkuDTO);
            if (ObjectUtil.isNotEmpty(supplierProductSkus)) {
                supplierProductSkuService.saveOrUpdateBatch(supplierProductSkus);
            }
        }
    }

    /**
     * 代销商品处理价格
     *
     * @param product 商品信息
     * @param skuList skuList
     */

    private <T> void handleConsignmentPrices(T product, List<StorageSku> skuList) {
        ConsignmentPriceSettingDTO consignmentPriceSetting = getObjectConsignmentPriceSetting(product);
        if (consignmentPriceSetting != null) {
            // 代销商品重新处理逻辑
            consignmentPriceSetting.consignmentCalculate(consignmentPriceSetting, skuList);

        }

    }

    private <T> ConsignmentPriceSettingDTO getObjectConsignmentPriceSetting(T product) {
        if (product instanceof ProductVO) {
            return ((ProductVO) product).getExtra().getConsignmentPriceSetting();
        } else if (product instanceof SupplierIssueProductListVO) {
            return ((SupplierIssueProductListVO) product).getExtra().getConsignmentPriceSetting();
        } else if (product instanceof PlatformProductVO) {
            return ((PlatformProductVO) product).getExtra().getConsignmentPriceSetting();
        }
        return null;
    }


    /**
     * 修改商品信息 mysql redis es
     *
     * @param product 商品信息
     * @param extra   商品额外信息
     */
    private void updateProduct2DB(Product product, ProductExtraDTO extra) {
        Integer update = RedisUtil.doubleDeletion(
                () -> baseMapper.update(product, Wrappers.lambdaQuery(Product.class)
                        .eq(Product::getId, product.getId())
                        .eq(Product::getShopId, product.getShopId())
                ),
                RedisUtil.key(GoodsConstant.GOODS_DETAIL_CACHE_KEY, product.getShopId(), product.getId()
                ));
        GoodsError.PRODUCT_UPDATE_FAIL.trueThrow(update < CommonPool.NUMBER_ONE);
        sendProductBroadcast(extra.getShopCategory(), extra.getPlatformCategory(), product, GoodsRabbit.GOODS_UPDATE.exchange(), GoodsRabbit.GOODS_UPDATE.routingKey());
    }


    @Autowired
    @Lazy
    public void setStorageRpcService(StorageRpcService storageRpcService) {
        this.storageRpcService = storageRpcService;
    }

    @Autowired
    @Lazy
    public void setUserRpcService(UserRpcService userRpcService) {
        this.userRpcService = userRpcService;
    }

    @Autowired
    @Lazy
    public void setShopRpcService(ShopRpcService shopRpcService) {
        this.shopRpcService = shopRpcService;
    }

    @Autowired
    @Lazy
    public void setGoodsRpcService(GoodsRpcService goodsRpcService) {
        this.goodsRpcService = goodsRpcService;
    }

    @Autowired
    @Lazy
    public void setSupplierProductSkuService(ISupplierProductSkuService supplierProductSkuService) {
        this.supplierProductSkuService = supplierProductSkuService;
    }
}
