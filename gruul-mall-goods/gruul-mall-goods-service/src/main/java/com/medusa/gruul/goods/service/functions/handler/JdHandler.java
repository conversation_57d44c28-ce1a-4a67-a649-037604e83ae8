package com.medusa.gruul.goods.service.functions.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.goods.api.constant.GoodsConstant;
import com.medusa.gruul.goods.api.enums.CopyGoodsType;
import com.medusa.gruul.goods.api.json.jd.JdItemJsonDTO;
import com.medusa.gruul.goods.api.model.dto.CopyGoodsDTO;
import com.medusa.gruul.goods.api.model.dto.CopyProductDTO;
import com.medusa.gruul.goods.api.model.enums.GoodsError;
import com.medusa.gruul.goods.service.functions.annotation.CopyAnnotation;
import com.medusa.gruul.storage.api.dto.SpecDTO;
import com.medusa.gruul.storage.api.dto.SpecGroupDTO;
import com.medusa.gruul.storage.api.enums.LimitType;
import com.medusa.gruul.storage.api.enums.StockType;
import io.vavr.control.Option;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/11
 * @describe 京东商品处理
 */
@Slf4j
@Component
@CopyAnnotation(CopyGoodsType.JD)
public class JdHandler extends AbstractCopyHandler {
    /**
     * 京东一键复制
     *
     * @param copyGoodsDto copyGoodsDto
     * @return CopyProductDTO
     */
    @Override
    public CopyProductDTO handler(CopyGoodsDTO copyGoodsDto) {
        String goodsUrl = copyGoodsDto.getGoodsUrl();
        String apikey = copyGoodsDto.getApikey();
        String copyGoodsUrl = GoodsConstant.COPY_GOODS_URL;
        String itemId = handlerJdUrl(goodsUrl);
        if (StrUtil.isEmpty(itemId)) {
            log.error("商品链接地址:{}", copyGoodsDto.getGoodsUrl());
            throw GoodsError.LINK_ERROR.exception();
        }
        String body = HttpUtil.createGet(StrUtil.format(copyGoodsUrl, CopyGoodsType.JD.getType(), apikey, itemId)).timeout(1000 * 60).execute().body();
        JSONObject entries = JSONUtil.parseObj(body);
        CopyProductDTO productDTO = new CopyProductDTO();
        if (!GoodsConstant.SUCCESS_CODE.equals(entries.get(GoodsConstant.RET_CODE).toString())) {
            throw GoodsError.API_EXCEPTION_99API.exception(entries.get(GoodsConstant.DATA).toString(), Integer.parseInt(entries.get(GoodsConstant.RET_CODE).toString()));
        }
        JSONObject item = entries.getJSONObject(GoodsConstant.DATA).getJSONObject(GoodsConstant.ITEM);
        JdItemJsonDTO jdItemJsonDto = item.toBean(JdItemJsonDTO.class);
        if (StrUtil.isNotEmpty(jdItemJsonDto.getName())) {
            productDTO.setName(StrUtil.sub(jdItemJsonDto.getName(), 0, 50));
        }
        List<String> images = jdItemJsonDto.getImages().stream().limit(CommonPool.NUMBER_SIX).collect(Collectors.toList());
        productDTO.setPic(images.get(CommonPool.NUMBER_ZERO));
        productDTO.setAlbumPics(StrUtil.join(StrUtil.COMMA, images));
        handlerJdSku(productDTO, jdItemJsonDto.getSkuProps(), jdItemJsonDto.getSaleProp(), jdItemJsonDto.getSku(), jdItemJsonDto.getPrice());
        return productDTO;
    }

    /**
     * 处理京东url
     *
     * @param goodsUrl 商品详情链接
     * @return 商品Id
     */
    private String handlerJdUrl(String goodsUrl) {
        if (!goodsUrl.contains(StrUtil.SLASH) || !goodsUrl.contains(GoodsConstant.AL_URL_SUF)) {
            return null;
        }
        return goodsUrl.substring(goodsUrl.lastIndexOf(StrUtil.SLASH) + 1, goodsUrl.lastIndexOf(GoodsConstant.AL_URL_SUF));
    }


    /**
     * 处理京东多规格
     *
     * @param productDTO  参数
     * @param skuPropsMap 规格值 {"1": ["暗紫色","深空黑色","金色","银色"],"2": ["128G","256G","512G","1TB"]}
     * @param salePropMap 规格名称
     * @param sku         规格
     *                    [{
     *                    "1": "暗紫色",
     *                    "2": "128G",
     *                    "stockState": 33,
     *                    "3": "",
     *                    "stockStateName": "现货",
     *                    "originalPrice": "7999.00",
     *                    "imagePath": "jfs/t1/150597/7/27516/21810/6343f8c8E952032fc/1f14645bd080c2c4.jpg",
     *                    "price": "7599.00",
     *                    "skuStatus": 1,
     *                    "skuId": 100042697319
     *                    }]
     * @param price       当前价
     */
    private void handlerJdSku(CopyProductDTO productDTO, Map<String, List<String>> skuPropsMap, Map<String, String> salePropMap, List<Map<String, String>> sku, String price) {
        List<SpecGroupDTO> specGroups = new ArrayList<>();
        Set<String> skuNameKeys = new HashSet<>();
        skuPropsMap.forEach((k, v) -> {
            if (CollUtil.isEmpty(v)) {
                return;
            }
            v = v.stream().filter(StrUtil::isNotBlank).toList();
            if (CollUtil.isEmpty(v)) {
                return;
            }
            SpecGroupDTO specGroupDTO = new SpecGroupDTO();
            //规格名称
            String name = salePropMap.get(k);
            if (StrUtil.isNotEmpty(name)) {
                skuNameKeys.add(k);
                List<SpecDTO> specDtoList = v.stream().map(item -> {
                    SpecDTO specDTO = new SpecDTO();
                    specDTO.setName(item);
                    return specDTO;
                }).toList();
                specGroupDTO.setName(name);
                specGroupDTO.setChildren(specDtoList);
                specGroups.add(specGroupDTO);
            }
        });
        productDTO.setSpecGroups(specGroups);
        //处理sku
        List<CopyProductDTO.CopySkuDto> skus = sku.stream().map(map -> {
            CopyProductDTO.CopySkuDto skuDTO = new CopyProductDTO.CopySkuDto();
            skuDTO.setSpecs(skuNameKeys.stream().map(map::get).toList());
            skuDTO.setImage(map.get(GoodsConstant.IMAGE_PATH));
            //原价
            skuDTO.setPrice(Double.parseDouble(Option.of(map.get(GoodsConstant.ORIGINAL_PRICE)).filter(StrUtil::isNotBlank).getOrElse(price)));
            //现价
            skuDTO.setSalePrice(Double.parseDouble(Option.of(map.get(GoodsConstant.PRICE)).filter(StrUtil::isNotBlank).getOrElse(price)));
            skuDTO.setInitSalesVolume(CommonPool.NUMBER_ZERO);
            skuDTO.setInitStock(CommonPool.NUMBER_ZERO);
            skuDTO.setLimitNum(CommonPool.NUMBER_ZERO);
            skuDTO.setStockType(StockType.UNLIMITED);
            skuDTO.setLimitType(LimitType.UNLIMITED);
            skuDTO.setWeight(BigDecimal.ZERO);
            return skuDTO;
        }).toList();
        productDTO.setSkus(skus);


    }
}
