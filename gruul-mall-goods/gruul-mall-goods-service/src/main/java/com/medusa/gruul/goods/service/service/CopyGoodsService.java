package com.medusa.gruul.goods.service.service;

import com.medusa.gruul.goods.api.model.dto.CopyGoodsDTO;
import com.medusa.gruul.goods.api.model.dto.CopyProductDTO;

/**
 * <AUTHOR>
 * @date 2023/1/30
 * @describe 商品一键复制
 */
public interface CopyGoodsService {
    /**
     * 一键复制
     *
     * @param copyGoodsDto copyGoodsDto
     * @return CopyProductDTO
     */
    CopyProductDTO getDetail(CopyGoodsDTO copyGoodsDto);
}
