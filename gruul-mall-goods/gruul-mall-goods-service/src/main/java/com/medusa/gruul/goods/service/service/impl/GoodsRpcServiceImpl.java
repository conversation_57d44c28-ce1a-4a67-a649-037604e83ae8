package com.medusa.gruul.goods.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.medusa.gruul.common.custom.aggregation.classify.dto.CategoryRankDTO;
import com.medusa.gruul.common.model.base.ShopProductKey;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.enums.SellType;
import com.medusa.gruul.common.model.exception.ServiceException;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.entity.ShopFollow;
import com.medusa.gruul.goods.api.entity.SupplierMerchant;
import com.medusa.gruul.goods.api.entity.SupplierProduct;
import com.medusa.gruul.goods.api.model.dto.ConsignmentPriceSettingDTO;
import com.medusa.gruul.goods.api.model.dto.ProductDTO;
import com.medusa.gruul.goods.api.model.dto.SupplierGoodsSpecSkuDTO;
import com.medusa.gruul.goods.api.model.dto.SupplierProductDeliverDTO;
import com.medusa.gruul.goods.api.model.enums.PricingType;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import com.medusa.gruul.goods.api.model.enums.SupplierProductType;
import com.medusa.gruul.goods.api.model.param.*;
import com.medusa.gruul.goods.api.model.vo.*;
import com.medusa.gruul.goods.api.rpc.GoodsRpcService;
import com.medusa.gruul.goods.service.addon.GoodsAddonSupporter;
import com.medusa.gruul.goods.service.client.YihuSupplierClient;
import com.medusa.gruul.goods.service.model.vo.ProductNumVo;
import com.medusa.gruul.goods.service.model.vo.SupplierVO;
import com.medusa.gruul.goods.service.mp.service.*;
import com.medusa.gruul.goods.service.service.ShopProductSyncService;
import com.medusa.gruul.order.api.entity.SupplierProductOrder;
import com.medusa.gruul.order.api.enums.SupplierProductOrderStatusEnum;
import com.medusa.gruul.order.api.model.SupplierProductOrderKeyDTO;
import com.medusa.gruul.order.api.model.SupplierUserDTO;
import com.medusa.gruul.order.api.rpc.OrderRpcService;
import com.medusa.gruul.storage.api.dto.ShopProductKeyDTO;
import com.medusa.gruul.storage.api.rpc.StorageRpcService;
import com.medusa.gruul.storage.api.vo.ProductStatisticsVO;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ description GoodsRpc实现
 * @date 2022-07-15 16:39
 */
@Service
@DubboService
@RequiredArgsConstructor
public class GoodsRpcServiceImpl implements GoodsRpcService {
    private final IProductService productService;
    private final IShopFollowService shopFollowService;
    private StorageRpcService storageRpcService;
    private OrderRpcService orderRpcService;
    private final GoodsAddonSupporter goodsAddonSupporter;

    private final ISupplierGoodsSkuService supplierGoodsSkuService;
    private final ISupplierService supplierService;
    private final ISupplierProductSkuService supplierProductSkuService;
    private final ISupplierMerchatService supplierMerchatService;
    private final YihuSupplierClient yihuSupplierClient;
    private final ISupplierProductService supplierProductService;
    private ShopProductSyncService shopProductSyncService;

    /**
     * 平台 获取商品信息
     *
     * @param platformProductParam 查询条件
     * @return 符合条件得商品信息
     */
    @Override
    public Page<PlatformProductVO> queryProductInfoByParam(PlatformProductParam platformProductParam) {
        return productService.queryProductInfoByParam(platformProductParam);
    }

    /**
     * 获取当前店铺上架商品数量
     *
     * @return 当前店铺上架商品数量
     */
    @Override
    public Result<Long> getShopProductByPutaway() {
        long count = productService.count(new QueryWrapper<Product>().eq("status", ProductStatus.SELL_ON));
        return Result.ok(ObjectUtil.isEmpty(count) ? CommonPool.NUMBER_ZERO : count);
    }

    /**
     * 查询运费模版id是否被商品使用
     *
     * @param templateId 物流模板id
     * @return Boolean
     */
    @Override
    public Boolean checkProductByTemplateId(Long templateId) {
        return productService.lambdaQuery().eq(Product::getFreightTemplateId, templateId).exists();
    }

    /**
     * 获取商品信息
     *
     * @param shopId    店铺id
     * @param productId 商品id
     * @return 商品信息
     */
    @Override
    public Product getProductInfo(@NotNull Long shopId, @NotNull Long productId) {
        return productService.getProductInfo(shopId, productId);
    }

    @Override
    public Map<ShopProductKey, Product> getProductBatch(Set<ShopProductKey> shopProductKeys) {
        return productService.getProductBatch(shopProductKeys);
    }

    /**
     * 平台端获取商品信息
     *
     * @param levelCategoryList     list<三级类目id>
     * @param platformCategoryParam 查询数据
     * @return 平台商品基础Vo
     */
    @Override
    public Result<Page<ApiPlatformProductVO>> getProductInfoByPlatformCategoryId(List<Long> levelCategoryList, PlatformCategoryParam platformCategoryParam) {
        Page<ApiPlatformProductVO> productInfoByPlatformCategoryId = productService.getProductInfoByPlatformCategoryId(levelCategoryList, platformCategoryParam);
        List<ApiPlatformProductVO> records = productInfoByPlatformCategoryId.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            Map<String, ProductStatisticsVO> productStatisticsMap = storageRpcService.getProductStatisticsMap(
                    productInfoByPlatformCategoryId.getRecords().stream()
                            .map(
                                    product -> new ShopProductKeyDTO()
                                            .setShopId(product.getSellType() == SellType.CONSIGNMENT ? product.getSupplierId() : product.getShopId())
                                            .setProductId(product.getId())
                            )
                            .collect(Collectors.toList())
            );
            records.forEach(record -> {
                SellType productSellType = record.getSellType();
                Long shopId = productSellType == SellType.CONSIGNMENT ? record.getSupplierId() : record.getShopId();
                ProductStatisticsVO productStatisticsVO = productStatisticsMap.get(
                        RedisUtil.key(shopId, record.getId())
                );

                if (productSellType == SellType.CONSIGNMENT) {
                    ConsignmentPriceSettingDTO consignmentPriceSetting = record.getExtra().getConsignmentPriceSetting();
                    if (consignmentPriceSetting != null) {
                        boolean isRegular = consignmentPriceSetting.getType() == PricingType.REGULAR;
                        long newHighestPrice = isRegular
                                ? consignmentPriceSetting.getSale()
                                : (productStatisticsVO.getHighestPrice() * consignmentPriceSetting.getSale() / 1000000);
                        productStatisticsVO.setHighestPrice(productStatisticsVO.getHighestPrice() + newHighestPrice);
                        long newLowestPrice = isRegular
                                ? consignmentPriceSetting.getScribe()
                                : (productStatisticsVO.getLowestPrice() * consignmentPriceSetting.getScribe() / 1000000);
                        productStatisticsVO.setLowestPrice(productStatisticsVO.getLowestPrice() + newLowestPrice);
                    }
                }
                record.setStatistics(productStatisticsVO);

            });
        }
        return Result.ok(productInfoByPlatformCategoryId);
    }

    /**
     * 获取平台三级类目下商品数量
     *
     * @param thirdIds 平台类目三级ids
     * @return map<平台类目ids, 商品数量>
     */
    @Override
    public Map<Long, Integer> getProductNumByPlatformThirdCategoryId(Set<Long> thirdIds) {
        List<ProductNumVo> productNum = productService.getProductNumByPlatformThirdCategoryId(thirdIds);
        return productNum.stream().collect(Collectors.toMap(ProductNumVo::getPlatformCategoryId, ProductNumVo::getNum));
    }

    /**
     * 获取随机商品
     *
     * @param productRandomParam 商品随机参数
     * @return 随机商品
     */
    @Override
    public Page<Product> randomGoods(ProductRandomParam productRandomParam) {
        return productService.randomGoods(productRandomParam);
    }

    /**
     * 根据平台三级类目ids 获取  ApiProductVO
     *
     * @param categoryRank 类目等级dto
     * @return ApiProductVO
     */
    @Override
    public Page<ApiProductVO> getApiProductInfoByPlatformCategoryId(CategoryRankDTO categoryRank) {
        return productService.getApiProductInfoByPlatformCategoryId(categoryRank);
    }


    /**
     * 获取条件商品信息 包含以删除商品信息
     *
     * @param shopId    店铺id
     * @param productId 商品id
     * @return 商品信息
     */
    @Override
    public Product getConditionProductInfo(Long shopId, Long productId) {
        return productService.getConditionProductInfo(shopId, productId);
    }

    /**
     * 用户收藏店铺数量
     *
     * @param userId 用户userid
     * @return 收藏店铺数量
     */
    @Override
    public Long shopFollow(Long userId) {
        return shopFollowService.lambdaQuery()
                .eq(ShopFollow::getUserId, userId)
                .count();
    }

    @Override
    public boolean getSigningCategoryProduct(Set<Long> signingCategorySecondIds, Long shopId) {
        return productService.getSigningCategoryProduct(signingCategorySecondIds, shopId);
    }

    /**
     * 根据{@code supplierId}和{@code productId}获取商品信息
     *
     * @param supplierId 供应商ID
     * @param productId  商品id
     * @return {@link Product}
     */
    @Override
    public Product getProductBySupplierIdAndProductId(Long supplierId, Long productId) {
        Product product = TenantShop.disable(() -> productService.getProductBySupplierIdAndProductId(supplierId, productId));
        if (product != null) {
            return product;
        }
        Set<ShopProductKey> shopProductKeySet = new HashSet<>();
        shopProductKeySet.add(new ShopProductKey().setProductId(productId).setShopId(supplierId));
        // 查询采购商品
        List<ProductDTO> supplierGoods = goodsAddonSupporter.getSupplierGoods(shopProductKeySet);
        if (CollectionUtil.isEmpty(supplierGoods)) {
            return null;
        }
        ProductDTO productDTO = supplierGoods.stream().findFirst().get();
        product = new Product();
        product.setId(productDTO.getId());
        product.setName(productDTO.getName());
        product.setPic(productDTO.getPic());
        return product;
    }


    @Lazy
    @Autowired
    public void setOrderRpcService(OrderRpcService orderRpcService) {
        this.orderRpcService = orderRpcService;
    }

    @Lazy
    @Autowired
    public void setStorageRpcService(StorageRpcService storageRpcService) {
        this.storageRpcService = storageRpcService;
    }

    @Lazy
    @Autowired
    public void setShopProductSyncService(ShopProductSyncService shopProductSyncService) {
        this.shopProductSyncService = shopProductSyncService;
    }

    @Override
    public SupplierGoodsSkuVO getSupperSkuBySkuId(Long skuId, Long shopId) {
        return supplierGoodsSkuService.getSupperSkuBySkuId(skuId, shopId);
    }

    @Override
    public List<SupplierGoodsSkuVO> getSupperSkuByProductId(Long productId, Long shopId) {
        return supplierGoodsSkuService.getSupperSkuByProductId(productId, shopId);
    }

    @Override
    public void saveOrUpdateSupperSku(SupplierGoodsSpecSkuDTO supplierGoodsSpecSkuDTO) {
        supplierGoodsSkuService.saveOrUpdateSupperSku(supplierGoodsSpecSkuDTO);
    }

    @Override
    public List<SupplierBankVO> getSupplierList() {
        List<SupplierVO> supplierList = supplierService.getSupplierList();
        return BeanUtil.copyToList(supplierList, SupplierBankVO.class);
    }

    @Override
    public IPage<ProductRenderVO> queryProductRenderVO(ProductRenderParam productRenderParam) {
        return productService.queryProductRenderVO(productRenderParam);
    }

    @Override
    public List<SupplierProductSkuVO> getSupplierSkuByProductId(Long productId) {
        return supplierProductSkuService.getSupplierSkuByProductId(productId);
    }

    @Override
    public SupplierProductSkuVO getSupplierSkuBySkuId(Long skuId) {
        return supplierProductSkuService.getSupplierSkuBySkuId(skuId);
    }

    @Override
    public SupplierProductCardVO supplierProductDeliver(SupplierProductDeliverDTO supplierProductDeliverDTO) {
        SupplierMerchant supplierMerchant = supplierMerchatService.lambdaQuery()
                .eq(SupplierMerchant::getSupplierId, supplierProductDeliverDTO.getSupplierId())
                .oneOpt().orElseThrow(() -> new ServiceException("供应商不存在"));
        Map<String, Object> params = Maps.newHashMap();
        params.put("appCode", supplierMerchant.getAppId());
        params.put("businessNo", supplierProductDeliverDTO.getOrderId());
        params.put("productCode", supplierProductDeliverDTO.getProductNo());
        params.put("orderNo", supplierProductDeliverDTO.getOrderNo());
        Result<Object> result = yihuSupplierClient.createCard(supplierMerchant, params);
        if (result.getCode() != 0) {
            throw new ServiceException(result.getMsg());
        }
        JSON parse = JSONUtil.parse(result.getData());
        JSONArray cardList = JSONUtil.parseArray(parse);
        //取第一个处理，正常只有一个
        SupplierProductCardVO supplierProductCardVO = JSONUtil.toBean(cardList.get(0).toString(), SupplierProductCardVO.class);
        return supplierProductCardVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void supplierProductOrderRefund(Long orderItemId) {
        SupplierProductOrderKeyDTO supplierProductOrderKeyDTO = new SupplierProductOrderKeyDTO();
        supplierProductOrderKeyDTO.setStatusEnum(SupplierProductOrderStatusEnum.SUCCESS)
                .setOrderItemId(orderItemId);
        List<SupplierProductOrder> supplierProductOrderList = orderRpcService.getSupplierProductOrder(supplierProductOrderKeyDTO);
        if (CollectionUtil.isEmpty(supplierProductOrderList)) {
            return;
        }
        //处理续购,续购目前只能单独下单
        SupplierProductOrder supplierOrder = supplierProductOrderList.get(0);
        SupplierProduct supplierProduct = this.getSupplierProductById(supplierOrder.getProductId());
        if (supplierProduct == null) {
            return;
        }
        //如果是续购
        if (SupplierProductType.RIGHT_PRODUCT.getValue().equals(supplierProduct.getType().getValue())) {
            this.cancelCardRightRefund(supplierOrder.getOrderNo());
        } else {
            //去重供应商订单后调用更新虚拟包裹激活码
            supplierProductOrderList.stream().map(supplierProductOrder -> supplierProductOrder.getOrderItemId())
                    .collect(Collectors.toSet()).stream()
                    .forEach(supplierProductOrder -> orderRpcService.updateVirtualPackageActivateCode(orderItemId));
            //调用供应商退款 更新供应商订单信息
            supplierProductOrderList.forEach(supplierProductOrder -> {
                SupplierMerchant supplierMerchant = supplierMerchatService.lambdaQuery()
                        .eq(SupplierMerchant::getSupplierId, supplierProductOrder.getSupplierId())
                        .oneOpt().orElseThrow(() -> new ServiceException("供应商不存在"));
                Map<String, Object> params = Maps.newHashMap();
                params.put("appCode", supplierMerchant.getAppId());
                params.put("businessNo", supplierProductOrder.getId());
                params.put("productCode", supplierProductOrder.getProductNo());
                params.put("orderNo", supplierProductOrder.getOrderNo());
                Result<Object> result = yihuSupplierClient.cancelCard(supplierMerchant, params);
                if (result.getCode() != 0) {
                    throw new ServiceException(result.getMsg());
                }
                SupplierProductOrderKeyDTO update = new SupplierProductOrderKeyDTO();
                update.setOrderItemId(supplierProductOrder.getOrderItemId());
                update.setStatusEnum(SupplierProductOrderStatusEnum.REFUND);
                orderRpcService.updateSupplierProductOrder(update);
            });
        }
    }

    /**
     * 根据id获取商户
     *
     * @param id
     * @return
     */
    @Override
    public SupplierMerchant getSupplierMerchantById(Long id) {
        return supplierMerchatService.getById(id);
    }

    @Override
    public SupplierMerchant getSupplierMerchantByAppId(String appId) {
        return supplierMerchatService.lambdaQuery().eq(SupplierMerchant::getAppId, appId).one();
    }

    /**
     * 根据供应商id获取商户
     *
     * @param supplierId 供应商id
     * @return
     */
    @Override
    public String getTokenBySupplierId(Long supplierId, Long userId, Long supplierUserId) {
        SupplierMerchant supplierMerchant = supplierMerchatService.lambdaQuery().eq(SupplierMerchant::getSupplierId, supplierId).one();
        SupplierUserDTO supplierUserDTO = orderRpcService.querySupplier(supplierId, userId, supplierUserId);
        return yihuSupplierClient.getToken(supplierMerchant, String.valueOf(supplierUserDTO.getSupplierUserId()));

    }

    @Override
    public String getTokenBySupplierId(Long supplierId, Long userId) {
        SupplierUserDTO supplierUserDTO = orderRpcService.querySupplier(supplierId, userId, null);
        if (supplierUserDTO == null) {
            return null;
        }
        SupplierMerchant supplierMerchant = supplierMerchatService.lambdaQuery().eq(SupplierMerchant::getSupplierId, supplierUserDTO.getSupplierId()).one();
        if (supplierMerchant == null) {
            return null;
        }
        return yihuSupplierClient.getToken(supplierMerchant, String.valueOf(supplierUserDTO.getSupplierUserId()));

    }

    /**
     * 续购校验
     *
     * @param checkParams
     */
    @Override
    public void checkRepurchase(Map<String, Object> checkParams) {
//        String appCode = String.valueOf(checkParams.get("appCode"));
//        SupplierMerchant supplierMerchant = supplierMerchatService.lambdaQuery().eq(SupplierMerchant::getAppId, appCode).one();
//        if (null == supplierMerchant) {
//            throw new ServiceException("供应商不存在");
//        }
//        yihuSupplierClient.repurchaseCheck(supplierMerchant, checkParams);

    }

    @Override
    public Result<Object> notifyYHRepurchaseSuccess(NotifyYHParam notifyYHParam) {
        String appCode = notifyYHParam.getAppCode();
        SupplierMerchant supplierMerchant = supplierMerchatService.lambdaQuery().eq(SupplierMerchant::getAppId, appCode).one();
        Result<Object> result = yihuSupplierClient.notifyYHRepurchaseSuccess(supplierMerchant, notifyYHParam);
        //更新订单状态
        SupplierProductOrderKeyDTO update = new SupplierProductOrderKeyDTO();
        update.setOrderNo(notifyYHParam.getOutTradeNo());
        update.setStatusEnum(SupplierProductOrderStatusEnum.SUCCESS);
        orderRpcService.updateSupplierProductOrder(update);
        return result;
    }

    @Override
    public void cancelCardRightRefund(String orderNo) {
        SupplierProductOrderKeyDTO supplierProductOrderKeyDTO = new SupplierProductOrderKeyDTO();
        supplierProductOrderKeyDTO.setStatusEnum(SupplierProductOrderStatusEnum.SUCCESS)
                .setOrderNo(orderNo);
        List<SupplierProductOrder> supplierProductOrderList = orderRpcService.getSupplierProductOrder(supplierProductOrderKeyDTO);
        if (CollectionUtil.isEmpty(supplierProductOrderList)) {
            return;
        }
        SupplierProductOrder supplierProductOrder = supplierProductOrderList.get(0);
        SupplierMerchant supplierMerchant = supplierMerchatService.lambdaQuery()
                .eq(SupplierMerchant::getSupplierId, supplierProductOrder.getSupplierId())
                .oneOpt().orElseThrow(() -> new ServiceException("供应商不存在"));

        String sendParamo = goodsAddonSupporter.getSendParamByOrderNo(orderNo);
        NotifyYHParam notifyYHParam = JSONUtil.toBean(sendParamo, NotifyYHParam.class);
        notifyYHParam.setTimestamp(System.currentTimeMillis());
        Result<Object> result = yihuSupplierClient.cancelCardRight(supplierMerchant, notifyYHParam);
        if (result.getCode() != 0) {
            throw new ServiceException(result.getMsg());
        }
        SupplierProductOrderKeyDTO update = new SupplierProductOrderKeyDTO();
        update.setOrderItemId(supplierProductOrder.getOrderItemId());
        update.setStatusEnum(SupplierProductOrderStatusEnum.REFUND);
        orderRpcService.updateSupplierProductOrder(update);
    }


    @Override
    public SupplierProduct getSupplierProductById(Long id) {
        return supplierProductService.getById(id);
    }

    @Override
    public List<Product> getProductInfoByIds(Set<Long> ids) {
        return TenantShop.disable(() -> productService.lambdaQuery().select(Product::getId,Product::getProductType).in(Product::getId, ids).list());
    }
}
