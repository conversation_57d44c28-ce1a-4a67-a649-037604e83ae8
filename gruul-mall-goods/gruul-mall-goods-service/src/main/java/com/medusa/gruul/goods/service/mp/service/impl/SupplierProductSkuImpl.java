package com.medusa.gruul.goods.service.mp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.goods.api.entity.SupplierProductSku;
import com.medusa.gruul.goods.api.model.vo.SupplierProductSkuVO;
import com.medusa.gruul.goods.service.mp.mapper.SupplierProductSkuMapper;
import com.medusa.gruul.goods.service.mp.service.ISupplierProductSkuService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 供应商产品 服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SupplierProductSkuImpl extends ServiceImpl<SupplierProductSkuMapper, SupplierProductSku> implements ISupplierProductSkuService {

    @Override
    public boolean saveOrUpdateBatch(Collection<SupplierProductSku> entityList, int batchSize) {
        if (entityList.isEmpty()) {
            return false;
        }

        entityList.forEach(supplierProductSku -> {
            if (supplierProductSku.getId() == null) {
                SupplierProductSku sku = this.lambdaQuery()
                        .select(SupplierProductSku::getId)
                        .eq(SupplierProductSku::getStorageSkuId, supplierProductSku.getStorageSkuId())
                        .one();
                if (null != sku) {
                    supplierProductSku.setId(sku.getId());
                }
            }
        });
        return super.saveOrUpdateBatch(entityList, batchSize);
    }

    @Override
    public List<SupplierProductSkuVO> getSupplierSkuByProductId(Long productId) {
        return baseMapper.getSupplierSkuByProductId(productId);
    }

    @Override
    public SupplierProductSkuVO getSupplierSkuBySkuId(Long skuId) {
        return baseMapper.getSupplierSkuBySkuId(skuId);
    }
}
