package com.medusa.gruul.goods.service.mp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.goods.api.entity.SupplierMerchant;
import com.medusa.gruul.goods.service.mp.mapper.SupplierMerchantMapper;
import com.medusa.gruul.goods.service.mp.service.ISupplierMerchatService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 供应商 服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SupplierMerchantImpl extends ServiceImpl<SupplierMerchantMapper, SupplierMerchant> implements ISupplierMerchatService {



}
