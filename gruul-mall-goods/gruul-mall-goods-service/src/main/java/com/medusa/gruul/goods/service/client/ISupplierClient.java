package com.medusa.gruul.goods.service.client;


import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.goods.api.entity.SupplierMerchant;
import com.medusa.gruul.goods.api.model.param.NotifyYHParam;

import java.util.Map;

/**
 * 供应商接口
 */
public interface ISupplierClient {
    /**
     * 调用创建卡
     */
    Result<Object> createCard(SupplierMerchant supplierMerchant, Map<String, Object> params);
    /**
     * 销卡
     */
    Result<Object> cancelCard(SupplierMerchant supplierMerchant, Map<String, Object> params);
    /**
     * 销卡权益续购
     */
    Result<Object> cancelCardRight(SupplierMerchant supplierMerchant, NotifyYHParam notifyYHParam);

    default String convertRequestUrl(String baseUrl, String apiUrl) {
        if (baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }
        if (!apiUrl.startsWith("/")) {
            baseUrl += "/";
        }
        return baseUrl + apiUrl;
    }
    /**
     * 获取token
     * @param userId 医护返回的userId
     * @return
     */
    String getToken(SupplierMerchant supplierMerchantDTO, String userId);

    void repurchaseCheck(SupplierMerchant supplierMerchant,Map<String,Object> repurchaseCheckParam);

    Result<Object> notifyYHRepurchaseSuccess(SupplierMerchant supplierMerchant, NotifyYHParam notifyYHParam);
}
