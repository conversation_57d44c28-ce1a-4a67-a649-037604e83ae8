package com.medusa.gruul.goods.service.service.impl;

import com.medusa.gruul.common.web.handler.Handler;
import com.medusa.gruul.common.web.util.SpringUtils;
import com.medusa.gruul.goods.api.model.dto.CopyGoodsDTO;
import com.medusa.gruul.goods.api.model.dto.CopyProductDTO;
import com.medusa.gruul.goods.service.functions.annotation.CopyAnnotation;
import com.medusa.gruul.goods.service.properties.CopyGoodsProperties;
import com.medusa.gruul.goods.service.service.CopyGoodsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023/1/30
 * @describe 商品一键复制
 */
@Service
@RequiredArgsConstructor
public class CopyGoodsServiceImpl implements CopyGoodsService {
    private final CopyGoodsProperties copyGoodsProperties;

    /**
     * 一键复制
     *
     * @param copyGoodsDto 参数
     * @return ProductDTO
     */
    @Override
    public CopyProductDTO getDetail(CopyGoodsDTO copyGoodsDto) {
        copyGoodsDto.setApikey(copyGoodsProperties.getApikey());
        Handler<CopyProductDTO> handler = SpringUtils.getBean(CopyAnnotation.class, copyGoodsDto.getCopyGoodsType());
        CopyProductDTO handle = handler.handle(copyGoodsDto);
        List<CopyProductDTO.CopySkuDto> updatedSkus = handle.getSkus().stream()
                .peek(copySkuDto -> {
                    copySkuDto.setMinimumPurchase(1L);
                })
                .collect(Collectors.toList());
        handle.setSkus(updatedSkus);
        return handle;
    }
}
