package com.medusa.gruul.goods.service.functions.handler;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.goods.api.constant.GoodsConstant;
import com.medusa.gruul.goods.api.enums.CopyGoodsType;
import com.medusa.gruul.goods.api.json.al.AlDataJsonDTO;
import com.medusa.gruul.goods.api.json.al.AlSkuPropsJsonDTO;
import com.medusa.gruul.goods.api.model.dto.CopyGoodsDTO;
import com.medusa.gruul.goods.api.model.dto.CopyProductDTO;
import com.medusa.gruul.goods.api.model.enums.GoodsError;
import com.medusa.gruul.goods.service.functions.annotation.CopyAnnotation;
import com.medusa.gruul.storage.api.dto.SpecDTO;
import com.medusa.gruul.storage.api.dto.SpecGroupDTO;
import com.medusa.gruul.storage.api.enums.LimitType;
import com.medusa.gruul.storage.api.enums.StockType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/11
 * @describe 阿里商品处理
 */
@Slf4j
@Component
@CopyAnnotation(CopyGoodsType.AliBaBa)
public class AlibabaHandler extends AbstractCopyHandler {
    /**
     * 阿里一键复制
     *
     * @param copyGoodsDto copyGoodsDto
     * @return CopyProductDTO
     */
    @Override
    public CopyProductDTO handler(CopyGoodsDTO copyGoodsDto) {
        String goodsUrl = copyGoodsDto.getGoodsUrl();
        String itemId = handlerAlUrl(goodsUrl);
        if (StrUtil.isEmpty(itemId)) {
            log.error("商品链接地址:{}", copyGoodsDto.getGoodsUrl());
            throw GoodsError.LINK_ERROR.exception();
        }
        String url = StrUtil.format(GoodsConstant.COPY_GOODS_URL, CopyGoodsType.AliBaBa.getType(), copyGoodsDto.getApikey(), itemId);
        String body = HttpUtil.createGet(url).timeout(1000 * 60).execute().body();
        JSONObject bodyJson = JSONUtil.parseObj(body);

        if (!GoodsConstant.SUCCESS_CODE.equals(bodyJson.get(GoodsConstant.RET_CODE).toString())) {
            throw GoodsError.API_EXCEPTION_99API.exception(bodyJson.get(GoodsConstant.DATA).toString(), Integer.parseInt(bodyJson.get(GoodsConstant.RET_CODE).toString()));
        }
        CopyProductDTO productDTO = new CopyProductDTO();
        JSONObject dataJson = bodyJson.getJSONObject(GoodsConstant.DATA);
        AlDataJsonDTO alDataJsonDto = dataJson.toBean(AlDataJsonDTO.class);
        if (StrUtil.isNotEmpty(alDataJsonDto.getTitle())) {
            productDTO.setName(StrUtil.sub(alDataJsonDto.getTitle(), 0, 50));
        }
        List<String> images = alDataJsonDto.getImages();
        images = images.stream().limit(CommonPool.NUMBER_SIX).collect(Collectors.toList());
        productDTO.setPic((images.get(CommonPool.NUMBER_ZERO)));
        productDTO.setAlbumPics(StrUtil.join(StrUtil.COMMA, images));
        productDTO.setVideoUrl(alDataJsonDto.getVideoUrl());
        productDTO.setDetail(alDataJsonDto.getDesc());
        handlerAlSku(productDTO, alDataJsonDto);
        return productDTO;
    }

    /**
     * 处理阿里商品链接
     *
     * @param goodsUrl 商品链接
     * @return 商品Id
     */
    private String handlerAlUrl(String goodsUrl) {
        if (!goodsUrl.contains(StrUtil.SLASH) || !goodsUrl.contains(GoodsConstant.AL_URL_SUF)) {
            return null;
        }
        return goodsUrl.substring(goodsUrl.lastIndexOf(StrUtil.SLASH) + 1, goodsUrl.indexOf(GoodsConstant.AL_URL_SUF));
    }

    /**
     * 阿里sku处理
     */
    @SuppressWarnings("all")
    private void handlerAlSku(CopyProductDTO productDTO, AlDataJsonDTO alDataJsonDto) {
        //规格名称
        List<AlSkuPropsJsonDTO> skuProps = alDataJsonDto.getSkuProps();
        List<SpecGroupDTO> specGroups = skuProps.stream().map(skuProp -> {
            List<AlSkuPropsJsonDTO.AlPropDto> value = skuProp.getValue();
            List<SpecDTO> children = value.stream().map(item -> {
                SpecDTO specDTO = new SpecDTO();
                specDTO.setName(item.getName());
                return specDTO;
            }).toList();

            SpecGroupDTO specGroupDTO = new SpecGroupDTO();
            specGroupDTO.setName(skuProp.getProp());
            specGroupDTO.setChildren(children);
            return specGroupDTO;
        }).toList();
        productDTO.setSpecGroups(specGroups);
        Map<String, JSONObject> skuMap = alDataJsonDto.getSkuMap();
        List<CopyProductDTO.CopySkuDto> skus = skuMap.entrySet().stream().map(entry -> {
            CopyProductDTO.CopySkuDto dto = new CopyProductDTO.CopySkuDto();
            List<String> specs = new ArrayList<>();
            if (entry.getKey().contains(XmlUtil.GT)) {
                String[] specValues = entry.getKey().split(XmlUtil.GT);
                specs = Arrays.stream(specValues).toList();
            } else {
                specs.add(entry.getKey());
            }
            //原价
            String price = (String) entry.getValue().get(GoodsConstant.PRICE);
            //现价
            String discountPrice = (String) entry.getValue().get(GoodsConstant.DISCOUNT_PRICE);
            if (StrUtil.isEmpty(price) || StrUtil.isEmpty(discountPrice)) {
                price = alDataJsonDto.getShowPriceRanges().get(CommonPool.NUMBER_ZERO).getPrice();
            }
            dto.setPrice((Double.parseDouble(price)));
            dto.setSalePrice((Double.parseDouble(price)));
            dto.setImage(productDTO.getPic());
            dto.setSpecs(specs);
            dto.setInitStock(Integer.parseInt(entry.getValue().get(GoodsConstant.CAN_BOOK_COUNT).toString()));
            dto.setInitSalesVolume(Integer.parseInt(entry.getValue().get(GoodsConstant.SALE_COUNT).toString()));
            dto.setLimitType(LimitType.UNLIMITED);
            dto.setStockType(StockType.LIMITED);
            dto.setLimitNum(CommonPool.NUMBER_ZERO);
            dto.setWeight(BigDecimal.ZERO);
            return dto;
        }).toList();
        productDTO.setSkus(skus);
    }
}
