package com.medusa.gruul.goods.service.mp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.goods.api.entity.SupplierProduct;
import com.medusa.gruul.goods.service.mp.mapper.SupplierProductMapper;
import com.medusa.gruul.goods.service.mp.service.ISupplierProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 供应商产品 服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SupplierProductImpl extends ServiceImpl<SupplierProductMapper, SupplierProduct> implements ISupplierProductService {



}
