package com.medusa.gruul.search.service.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * date 2022/10/31
 */
@Getter
@Setter
@ConfigurationProperties(prefix = "gruul.search")
public class SearchConfigurationProperties {

    /**
     * 线程池配置
     */
    private TaskThreadPool threadPool = new TaskThreadPool();


    /**
     * 线程池配置详情
     */
    @Getter
    @Setter
    public static class TaskThreadPool {

        /**
         * 线程池线程名前缀
         */
        private String threadNamePrefix = "Search-Future";
        /**
         * 核心线程数 - 适合4核CPU的I/O密集型任务
         */
        private int corePoolSize = 8;
        /**
         * 最大线程数 - 处理突发流量
         */
        private int maxPoolSize = 16;
        /**
         * 线程存活时间长度 - 空闲线程保持时间(秒)
         */
        private int keepAliveSeconds = 60;
        /**
         * 任务队列长度
         */
        private int queueCapacity = 1000;
    }

}
