package com.medusa.gruul.payment.service.properties;

import com.github.binarywang.wxpay.constant.WxPayConstants;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 支付Property
 *
 * <AUTHOR>
 * Description PayProperty.java
 * date 2022-07-28 09:32
 */
@Data
@ConfigurationProperties(prefix = "gruul.pay")
public class PaymentProperty {

	/**
	 * 回调url前缀
	 */
	private String notifyUrlPrefix;

	/**
	 * 前端回调页面
	 */
	private String returnUrl;

	/**
	 * 是否启用服务商模式
	 */
	private boolean enableServiceMode = false;

	/**
	 * 微信服务商支付配置
	 */
	private WxServiceConf wxServiceConf = new WxServiceConf();

	/**
	 * 线程池配置
	 */
	private TaskThreadPool threadPool = new TaskThreadPool();

	/**
	 * 微信支付服务商配置
	 *
	 * <AUTHOR>
	 */
	@Getter
	@Setter
	@ToString
	public static class WxServiceConf {

		/**
		 * 微信小程序 appid 必填 小程序/h5支付时使用
		 */
		private String appid;

		/**
		 * (商户进件时使用) 微信开放平台支付app的 appid app支付使用
		 */
		private String openAppid;

		/**
		 * (商户进件时使用) 网站域名 native支付使用
		 */
		private String domain;

		/**
		 * 服务商商户号
		 */
		private String mchId;

		/**
		 * 自营特约商户号
		 */
		private String selfSubMchid;

		/**
		 * 签名类型，目前支持HMAC-SHA256和MD5，默认为MD5
		 */
		private String signType = WxPayConstants.SignType.MD5;

		/**
		 * api v3秘钥
		 */
		private String apiV3Key;
		/**
		 * p12证书路径
		 */
		private String keyPath = "classpath:cert/apiclient_cert.p12";

		/**
		 * apiclient_cert.pem证书文件的绝对路径或者以classpath:开头的类路径.
		 */
		private String privateCertPath = "classpath:cert/apiclient_cert.pem";

		/**
		 * apiclient_key.pem证书文件的绝对路径或者以classpath:开头的类路径.
		 */
		private String privateKeyPath = "classpath:cert/apiclient_key.pem";


	}

	/**
	 * 线程池配置详情
	 */
	@Getter
	@Setter
	public static class TaskThreadPool {

		/**
		 * 线程池线程名前缀
		 */
		private String threadNamePrefix = "Payment-Future";
		/**
		 * 核心线程数 - 适合4核CPU的I/O密集型任务
		 */
		private int corePoolSize = 8;
		/**
		 * 最大线程数 - 处理突发流量
		 */
		private int maxPoolSize = 16;
		/**
		 * 线程存活时间长度 - 空闲线程保持时间(秒)
		 */
		private int keepAliveSeconds = 60;
		/**
		 * 任务队列长度
		 */
		private int queueCapacity = 1000;
	}


}
