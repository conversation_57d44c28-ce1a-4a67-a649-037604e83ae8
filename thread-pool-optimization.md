# 线程池配置统一优化方案

## 系统配置
- CPU: 4核
- 内存: 16G
- 业务特点: I/O密集型（数据库查询、RPC调用）

## 优化原则
1. **核心线程数**: CPU核数 × 2 = 8 (适合I/O密集型)
2. **最大线程数**: CPU核数 × 4 = 16 (处理突发流量)
3. **空闲存活时间**: 300秒 (减少线程创建销毁开销)
4. **队列容量**: 1000 (缓冲更多任务)

## 已优化的模块

### 1. Storage模块 ✅
- 文件: `gruul-mall-storage/gruul-mall-storage-service/src/main/java/com/medusa/gruul/storage/service/properties/StorageProperties.java`
- 配置前缀: `gruul.storage.task-thread-pool`

### 2. Goods模块 ✅
- 文件: `gruul-mall-goods/gruul-mall-goods-service/src/main/java/com/medusa/gruul/goods/service/properties/GoodsConfigurationProperties.java`
- 配置前缀: `gruul.goods.thread-pool`

### 3. Order模块 ✅
- 文件: `gruul-mall-order/gruul-mall-order-service/src/main/java/com/medusa/gruul/order/service/properties/OrderConfigurationProperties.java`
- 配置前缀: `gruul.order.thread-pool`

### 4. Search模块 ✅
- 文件: `gruul-mall-search/gruul-mall-search-service/src/main/java/com/medusa/gruul/search/service/properties/SearchConfigurationProperties.java`
- 配置前缀: `gruul.search.thread-pool`

### 5. Shop模块 ✅
- 文件: `gruul-mall-shop/gruul-mall-shop-service/src/main/java/com/medusa/gruul/shop/service/properties/ShopConfigurationProperties.java`
- 配置前缀: `gruul.shop.thread-pool`

### 6. User模块 ✅
- 文件: `gruul-mall-user/gruul-mall-user-service/src/main/java/com/medusa/gruul/user/service/properties/UserConfigurationProperties.java`
- 配置前缀: `gruul.user.thread-pool`

### 7. Payment模块 ✅
- 文件: `gruul-mall-payment/gruul-mall-payment-service/src/main/java/com/medusa/gruul/payment/service/properties/PaymentProperty.java`
- 配置前缀: `gruul.payment.thread-pool`

## 统一配置值
```yaml
# 应用到所有模块的配置
thread-pool:
  core-pool-size: 8          # CPU核数 × 2
  max-pool-size: 16          # CPU核数 × 4  
  keep-alive-seconds: 300    # 5分钟空闲存活时间
  queue-capacity: 1000       # 队列容量
```

## 配置文件位置
可以在各个模块的 `application.yml` 中添加：

```yaml
# Storage模块
gruul:
  storage:
    task-thread-pool:
      core-pool-size: 8
      max-pool-size: 16
      keep-alive-seconds: 300
      queue-capacity: 1000

# Goods模块
gruul:
  goods:
    thread-pool:
      core-pool-size: 8
      max-pool-size: 16
      keep-alive-seconds: 300
      queue-capacity: 1000

# Order模块
gruul:
  order:
    thread-pool:
      core-pool-size: 8
      max-pool-size: 16
      keep-alive-seconds: 300
      queue-capacity: 1000

# Search模块
gruul:
  search:
    thread-pool:
      core-pool-size: 8
      max-pool-size: 16
      keep-alive-seconds: 300
      queue-capacity: 1000

# Shop模块
gruul:
  shop:
    thread-pool:
      core-pool-size: 8
      max-pool-size: 16
      keep-alive-seconds: 300
      queue-capacity: 1000

# User模块
gruul:
  user:
    thread-pool:
      core-pool-size: 8
      max-pool-size: 16
      keep-alive-seconds: 300
      queue-capacity: 1000

# Payment模块
gruul:
  payment:
    thread-pool:
      core-pool-size: 8
      max-pool-size: 16
      keep-alive-seconds: 300
      queue-capacity: 1000
```

## 监控建议
1. 添加线程池状态监控日志
2. 监控线程池队列使用情况
3. 监控任务执行时间和异常率
4. 设置线程池饱和告警

## 预期效果
1. 减少 `InterruptedException` 发生频率
2. 提高系统并发处理能力
3. 更好的资源利用率
4. 减少线程创建销毁开销
